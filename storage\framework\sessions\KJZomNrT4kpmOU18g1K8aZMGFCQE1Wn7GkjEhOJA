a:5:{s:6:"_token";s:40:"Yi1joBrjNyBvZKkGlnqXBA5D5b2RGNzdJOUSewSa";s:10:"guest_user";O:15:"App\Models\User":32:{s:13:" * connection";N;s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:999999;s:8:"username";s:19:"Guest_68512bac8f78b";s:5:"email";s:14:"<EMAIL>";s:7:"balance";d:10000;s:5:"admin";i:0;s:6:"banned";i:0;s:6:"avatar";s:11:"default.png";s:6:"ref_id";N;s:9:"ref_money";i:0;s:5:"wager";i:0;s:10:"created_at";s:19:"2025-06-17 11:47:40";s:10:"updated_at";s:19:"2025-06-17 11:47:40";s:8:"is_guest";b:1;}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:17:{i:0;s:2:"id";i:1;s:8:"username";i:2;s:8:"password";i:3;s:6:"avatar";i:4;s:2:"ip";i:5;s:7:"balance";i:6;s:5:"wager";i:7;s:5:"vk_id";i:8;s:5:"tg_id";i:9;s:9:"unique_id";i:10;s:5:"admin";i:11;s:8:"youtuber";i:12;s:8:"ref_code";i:13;s:11:"referred_by";i:14;s:3:"ban";i:15;s:10:"bonus_time";i:16;s:9:"api_token";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}s:50:"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:999999;s:9:"_previous";a:1:{s:3:"url";s:31:"http://localhost:8000/blackjack";}s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}}