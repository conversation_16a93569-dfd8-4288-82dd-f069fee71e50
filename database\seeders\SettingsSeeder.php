<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('settings')->insert([
            'id' => 1,
            'site_name' => 'BrixCasino',
            'site_description' => 'Лучшее онлайн казино',
            'telegram_bot_token' => null,
            'telegram_channel' => null,
            'min_deposit' => 1.00,
            'min_withdraw' => 5.00,
            'max_withdraw' => 1000.00,
            'referral_bonus' => 0.50,
            'maintenance_mode' => '0',
            'blackjack_enabled' => 0,
            'dice_enabled' => 0,
            'mines_enabled' => 0,
            'wheel_enabled' => 0,
            'jackpot_enabled' => 0,
            'crash_enabled' => 0,
            'tech_work' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
