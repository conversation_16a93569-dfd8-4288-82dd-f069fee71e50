<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('site_name')->default('BrixCasino');
            $table->string('site_description')->nullable();
            $table->string('telegram_bot_token')->nullable();
            $table->string('telegram_channel')->nullable();
            $table->decimal('min_deposit', 10, 2)->default(1.00);
            $table->decimal('min_withdraw', 10, 2)->default(5.00);
            $table->decimal('max_withdraw', 10, 2)->default(1000.00);
            $table->decimal('referral_bonus', 5, 2)->default(0.50);
            $table->string('maintenance_mode')->default('0');
            $table->boolean('blackjack_enabled')->default(0);
            $table->boolean('roulette_enabled')->default(0);
            $table->boolean('coinflip_enabled')->default(0);
            $table->boolean('baccarat_enabled')->default(0);
            $table->boolean('dice_enabled')->default(0);
            $table->boolean('mines_enabled')->default(0);
            $table->boolean('wheel_enabled')->default(0);
            $table->boolean('jackpot_enabled')->default(0);
            $table->boolean('crash_enabled')->default(0);
            $table->boolean('tech_work')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
};
