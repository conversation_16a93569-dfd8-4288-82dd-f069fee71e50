<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Roulette extends Model
{
    use HasFactory;

    protected $table = 'roulette';

    protected $fillable = [
        'winning_number', 'status', 'total_bets'
    ];

    public function bets()
    {
        return $this->hasMany(RouletteBets::class, 'game_id');
    }

    /**
     * Get the color of a roulette number
     */
    public static function getNumberColor($number)
    {
        if ($number == 0) return 'green';
        
        $redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
        
        return in_array($number, $redNumbers) ? 'red' : 'black';
    }

    /**
     * Check if number is odd
     */
    public static function isOdd($number)
    {
        return $number > 0 && $number % 2 == 1;
    }

    /**
     * Check if number is even
     */
    public static function isEven($number)
    {
        return $number > 0 && $number % 2 == 0;
    }

    /**
     * Check if number is in first dozen (1-12)
     */
    public static function isFirstDozen($number)
    {
        return $number >= 1 && $number <= 12;
    }

    /**
     * Check if number is in second dozen (13-24)
     */
    public static function isSecondDozen($number)
    {
        return $number >= 13 && $number <= 24;
    }

    /**
     * Check if number is in third dozen (25-36)
     */
    public static function isThirdDozen($number)
    {
        return $number >= 25 && $number <= 36;
    }

    /**
     * Check if number is low (1-18)
     */
    public static function isLow($number)
    {
        return $number >= 1 && $number <= 18;
    }

    /**
     * Check if number is high (19-36)
     */
    public static function isHigh($number)
    {
        return $number >= 19 && $number <= 36;
    }

    /**
     * Get payout multiplier for bet type
     */
    public static function getPayoutMultiplier($betType)
    {
        $payouts = [
            'straight' => 35,      // Single number
            'split' => 17,         // Two numbers
            'street' => 11,        // Three numbers
            'corner' => 8,         // Four numbers
            'line' => 5,           // Six numbers
            'dozen' => 2,          // First/Second/Third dozen
            'column' => 2,         // Column bet
            'red' => 1,            // Red
            'black' => 1,          // Black
            'odd' => 1,            // Odd
            'even' => 1,           // Even
            'low' => 1,            // 1-18
            'high' => 1,           // 19-36
        ];

        return $payouts[$betType] ?? 0;
    }

    /**
     * Check if bet wins
     */
    public static function checkBetWin($betType, $betValue, $winningNumber)
    {
        switch ($betType) {
            case 'straight':
                return (int)$betValue == $winningNumber;
            
            case 'red':
                return self::getNumberColor($winningNumber) == 'red';
            
            case 'black':
                return self::getNumberColor($winningNumber) == 'black';
            
            case 'odd':
                return self::isOdd($winningNumber);
            
            case 'even':
                return self::isEven($winningNumber);
            
            case 'low':
                return self::isLow($winningNumber);
            
            case 'high':
                return self::isHigh($winningNumber);
            
            case 'first_dozen':
                return self::isFirstDozen($winningNumber);
            
            case 'second_dozen':
                return self::isSecondDozen($winningNumber);
            
            case 'third_dozen':
                return self::isThirdDozen($winningNumber);
            
            case 'split':
                $numbers = explode(',', $betValue);
                return in_array($winningNumber, array_map('intval', $numbers));
            
            default:
                return false;
        }
    }
}
