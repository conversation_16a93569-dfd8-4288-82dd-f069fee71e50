<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('email')->unique()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->decimal('balance', 10, 2)->default(0.00);
            $table->decimal('wager', 10, 2)->default(0.00);
            $table->string('avatar')->default('default.png');
            $table->string('ip')->nullable();
            $table->string('vk_id')->nullable();
            $table->string('tg_id')->nullable();
            $table->string('unique_id')->nullable();
            $table->boolean('admin')->default(0);
            $table->boolean('youtuber')->default(0);
            $table->string('ref_code')->nullable();
            $table->string('referred_by')->nullable();
            $table->boolean('ban')->default(0);
            $table->timestamp('bonus_time')->nullable();
            $table->string('api_token')->nullable();
            $table->string('videocard')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
};
