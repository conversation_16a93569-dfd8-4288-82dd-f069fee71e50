/**
 * notifyme.js v2.0
 * https://mwa.li
 *
 * Licensed under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 *
 * Copyright 2014, <PERSON>
 * http://www.mohammadwali.in
 */
!function(t,r,i){"use strict";var a=t(i),o=(t(r),"notifyme"),n=function(t){throw"error: Cannot Notify => "+t},m=function(t){"undefiend"==console.warn?console.log("Notify Warning: "+t):console.warn("Notify Warning: "+t)},e=function(t,r){for(var i=0;i<t.length;i++)if(t[i]===r)return!0;return!1},f=function(t,r,i){for(var o=["webkit","moz","MS","o",""],n=0;n<o.length;n++)o[n]||(r=r.toLowerCase()),a.on(o[n]+r,t,i)},s=function(t){t.parents("."+o+"-notification").removeClass(""+o+"-show"),setTimeout(function(){t.parents("."+o+"-notification").addClass(""+o+"-hide")},25)},d=function(r){var a=i.createElement("div"),n=i.createElement("div"),m=i.createElement("i"),e=i.createElement("p"),f=i.createElement("span");a.className=""+o+"-notification "+o+"-"+r.position+" "+o+"-"+r.type+" "+o+"-show",n.className=o+"-wrapper","error"==r.type?m.className=o+"-icon fa fa-times-circle":"success"==r.type?m.className=o+"-icon fa fa-check-circle":"warning"==r.type&&(m.className=o+"-icon fa fa-exclamation-triangle"),f.className="notifyme-close",i.body.appendChild(a),a.appendChild(n),a.appendChild(f),n.appendChild(m),n.appendChild(e),e.innerHTML=r.message,1==r.autohide&&setTimeout(function(){s(t(f))},r.autohideDelay)};if(t.notify=function(r){var i=["top-left","bottom-left","top-right","bottom-right"],a=["error","success","warning"],f={position:i[0]},x={message:"",type:"",autohide:1,autohideDelay:3000,position:i[3]};t.extend(x,r),""!=x.type||x.type.length||n("Type is not defined!"),e(a,x.type)||n("Uhh, invalid notify type!"),""!=x.message||x.message.length||n("Hmmm, Message seems to be empty or not defined!"),e(i,x.position)||(m("Oh, Invalid position switching to default!"),x.position=f.position),t("."+o+"-"+x.position).length&&s(t("."+o+"-"+x.position).find("."+o+"-close")),d(x)},!i.getElementById(o+"-style")){var x=i.createElement("style");x.id=o+"-style",x.innerHTML="@import url(https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css);.notifyme-notification,.notifyme-notification *,.notifyme-notification :after,.notifyme-notification :before{-webkit-box-sizing:border-box;box-sizing:border-box}.notifyme-notification.notifyme-hide{-webkit-animation-name:animFade;animation-name:animFade;-webkit-animation-duration:.25s;animation-duration:.25s;-webkit-animation-direction:reverse;animation-direction:reverse}.notifyme-notification.notifyme-show{-webkit-animation-duration:0.1s;animation-duration:0.1s;-webkit-animation-timing-function:linear;animation-timing-function:linear;pointer-events:auto}.notifyme-notification{border-radius:10px;position:fixed;background:rgba(42,45,50,.85);padding:18px 25px;line-height:1.4;pointer-events:none;color:rgba(250,251,255,.95);font-size:90%;font-family:'Helvetica Neue','Segoe UI',Helvetica,Arial,sans-serif;max-width:370px;z-index:9999999999999}.notifyme-notification p{margin:0;line-height:1.3;font-size:14px}.notifyme-notification a{opacity:.7;font-weight:700;text-decoration:none}.notifyme-notification a:focus,.notifyme-notification a:hover{color:#fff!important;opacity:1}.notifyme-close{width:20px;height:20px;position:absolute;right:4px;top:4px;overflow:hidden;text-indent:100%;cursor:pointer;-webkit-backface-visibility:hidden;backface-visibility:hidden}.notifyme-close::after,.notifyme-close::before{content:'';position:absolute;width:3px;height:60%;top:50%;left:50%;background:#6e6e6e}.notifyme-close::after{-webkit-transform:translate(-50%,-50%) rotate(-45deg);transform:translate(-50%,-50%) rotate(-45deg)}.notifyme-close::before{-webkit-transform:translate(-50%,-50%) rotate(45deg);transform:translate(-50%,-50%) rotate(45deg)}.notifyme-close:focus,.notifyme-close:hover{outline:0}.notifyme-close:hover::after,.notifyme-close:hover::before{background:#fff!important}@-webkit-keyframes animFade{0%{opacity:0}100%{opacity:1}}@keyframes animFade{0%{opacity:0}100%{opacity:1}}@-webkit-keyframes animJelly{0%{-webkit-transform:matrix3d(0.7,0,0,0,0,.7,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.7,0,0,0,0,.7,0,0,0,0,1,0,0,0,0,1)}2.083333%{-webkit-transform:matrix3d(0.75266,0,0,0,0,.76342,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.75266,0,0,0,0,.76342,0,0,0,0,1,0,0,0,0,1)}4.166667%{-webkit-transform:matrix3d(0.81071,0,0,0,0,.84545,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.81071,0,0,0,0,.84545,0,0,0,0,1,0,0,0,0,1)}6.25%{-webkit-transform:matrix3d(0.86808,0,0,0,0,.9286,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.86808,0,0,0,0,.9286,0,0,0,0,1,0,0,0,0,1)}8.333333%{-webkit-transform:matrix3d(0.92038,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.92038,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}10.416667%{-webkit-transform:matrix3d(0.96482,0,0,0,0,1.05202,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.96482,0,0,0,0,1.05202,0,0,0,0,1,0,0,0,0,1)}12.5%{-webkit-transform:matrix3d(1,0,0,0,0,1.08204,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1,0,0,0,0,1.08204,0,0,0,0,1,0,0,0,0,1)}14.583333%{-webkit-transform:matrix3d(1.02563,0,0,0,0,1.09149,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.02563,0,0,0,0,1.09149,0,0,0,0,1,0,0,0,0,1)}16.666667%{-webkit-transform:matrix3d(1.04227,0,0,0,0,1.08453,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.04227,0,0,0,0,1.08453,0,0,0,0,1,0,0,0,0,1)}18.75%{-webkit-transform:matrix3d(1.05102,0,0,0,0,1.06666,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.05102,0,0,0,0,1.06666,0,0,0,0,1,0,0,0,0,1)}20.833333%{-webkit-transform:matrix3d(1.05334,0,0,0,0,1.04355,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.05334,0,0,0,0,1.04355,0,0,0,0,1,0,0,0,0,1)}22.916667%{-webkit-transform:matrix3d(1.05078,0,0,0,0,1.02012,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.05078,0,0,0,0,1.02012,0,0,0,0,1,0,0,0,0,1)}25%{-webkit-transform:matrix3d(1.04487,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.04487,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}27.083333%{-webkit-transform:matrix3d(1.03699,0,0,0,0,.98534,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.03699,0,0,0,0,.98534,0,0,0,0,1,0,0,0,0,1)}39.583333%{-webkit-transform:matrix3d(0.99617,0,0,0,0,.99433,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99617,0,0,0,0,.99433,0,0,0,0,1,0,0,0,0,1)}41.666667%{-webkit-transform:matrix3d(0.99368,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99368,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}43.75%{-webkit-transform:matrix3d(0.99237,0,0,0,0,1.00413,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99237,0,0,0,0,1.00413,0,0,0,0,1,0,0,0,0,1)}45.833333%{-webkit-transform:matrix3d(0.99202,0,0,0,0,1.00651,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99202,0,0,0,0,1.00651,0,0,0,0,1,0,0,0,0,1)}56.25%{-webkit-transform:matrix3d(0.99705,0,0,0,0,1.0016,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99705,0,0,0,0,1.0016,0,0,0,0,1,0,0,0,0,1)}58.333333%{-webkit-transform:matrix3d(0.99822,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99822,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}60.416667%{-webkit-transform:matrix3d(0.99921,0,0,0,0,.99884,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(0.99921,0,0,0,0,.99884,0,0,0,0,1,0,0,0,0,1)}62.5%{-webkit-transform:matrix3d(1,0,0,0,0,.99816,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1,0,0,0,0,.99816,0,0,0,0,1,0,0,0,0,1)}64.583333%{-webkit-transform:matrix3d(1.00057,0,0,0,0,.99795,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00057,0,0,0,0,.99795,0,0,0,0,1,0,0,0,0,1)}66.666667%{-webkit-transform:matrix3d(1.00095,0,0,0,0,.99811,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00095,0,0,0,0,.99811,0,0,0,0,1,0,0,0,0,1)}68.75%{-webkit-transform:matrix3d(1.00114,0,0,0,0,.99851,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00114,0,0,0,0,.99851,0,0,0,0,1,0,0,0,0,1)}70.833333%{-webkit-transform:matrix3d(1.00119,0,0,0,0,.99903,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00119,0,0,0,0,.99903,0,0,0,0,1,0,0,0,0,1)}72.916667%{-webkit-transform:matrix3d(1.00114,0,0,0,0,.99955,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00114,0,0,0,0,.99955,0,0,0,0,1,0,0,0,0,1)}75%{-webkit-transform:matrix3d(1.001,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.001,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}77.083333%{-webkit-transform:matrix3d(1.00083,0,0,0,0,1.00033,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00083,0,0,0,0,1.00033,0,0,0,0,1,0,0,0,0,1)}79.166667%{-webkit-transform:matrix3d(1.00063,0,0,0,0,1.00052,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1.00063,0,0,0,0,1.00052,0,0,0,0,1,0,0,0,0,1)}100%{-webkit-transform:matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);transform:matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)}}.notifyme-wrapper i.fa.notifyme-icon{position:absolute;left:20px;top:53%;font-size:22px;margin-top:-11px}.notifyme-wrapper{padding-left:20px;padding-right:5px}.notifyme-notification.notifyme-success{background:rgba(22, 145, 81, .7)}.notifyme-notification.notifyme-success .notifyme-close::after,.notifyme-notification.notifyme-success .notifyme-close::before{background:#FFFFFF}.notifyme-notification.notifyme-success a{color:#007330}.notifyme-notification.notifyme-error{background:rgba(216,51,51,.7)}.notifyme-notification.notifyme-error .notifyme-close::after,.notifyme-notification.notifyme-error .notifyme-close::before{background:#FFFFFF}.notifyme-notification.notifyme-error a{color:#7c1313}.notifyme-notification.notifyme-warning{background:#ffe008;color:#c8922f}.notifyme-notification.notifyme-warning .notifyme-close::after,.notifyme-notification.notifyme-warning .notifyme-close::before{background:#c8922f}.notifyme-notification.notifyme-warning a{color:#a97515}.notifyme-notification.notifyme-top-left{top:30px;left:30px}.notifyme-notification.notifyme-top-right{top:30px;right:30px}.notifyme-notification.notifyme-bottom-left{bottom:30px;left:30px}.notifyme-notification.notifyme-bottom-right{bottom:20px;right:20px}",i.getElementsByTagName("head")[0].appendChild(x)}f(t(".notifyme-notification"),"AnimationEnd",function(){t(".notifyme-notification.notifyme-hide").remove()}),a.on("click",".notifyme-close",function(){s(t(this))})}(window.jQuery,window,document);