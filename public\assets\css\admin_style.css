@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
* {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style-type: none;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

:root {
    --color-main-bg: #16171d;
    --color-inner: #1B1C24;
    --color-nav-active: #D9D9D907;
    --main-color: #2AC800;
    --main-grey-color: #a9a9a9;
}

body {
    height: 100vh;
    background: var(--color-main-bg);
}

body::-webkit-scrollbar {
    width: 0;
}

[class*="__container"] {
    position: relative;
    margin: 0 auto;
    max-width: 1280px;
    width: 100%;
    background-color: var(--color-inner);
    border-radius: 15px;
}

.header {
    position: relative;
    height: 70px;
    background: var(--color-inner);
    padding: 0 30px;
    z-index: 1;
    display: flex;
    align-items: center;
}

.logo {
    text-align: center;
    padding: 0;
    margin-left: 0;
    display: flex;
    justify-content: center;
    margin-top: 15px;
    align-items: center;
    z-index: 10;
    font-family: 'Roboto', sans-serif;
    font-style: normal;
    font-weight: 900;
    font-size: 32px;
    line-height: 42px;
    color: #FFFFFF;
}

.green-x {
    color: #2AC800;
}

.admin__info {
    display: flex;
    margin-left: auto;
    gap: 10px;
    align-items: center;
}

.admin__name {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #FFFFFF;
}

.admin__avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50px;
}

.admin__items {
    margin-top: 35px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 200px;
}

.admin_menu {
    position: fixed;
    z-index: 3;
    top: 0;
    left: 0;
    bottom: 0;
    background: var(--color-inner);
    display: flex;
    flex-direction: column;
}

.admin__item.active .admin__item_name {
    color: var(--main-color);
}

.admin__item.active a {
    color: var(--main-color);
}

.admin__item {
    display: flex;
    align-items: center;
    gap: 13px;
    color: #FFFFFF;
    align-items: center
}

.admin__item a {
    margin-left: 15px;
    padding: 6px;
    font-size: 20px;
    color: #FFFFFF;
    background-color: transparent;
    border-radius: 10px;
    -webkit-transition: color .3s,background-color .3s;
    transition: color .3s,background-color .3s;
}

.admin__item_name {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
}

.admin__content {
    display: flex;
    flex-direction: column;
    top: 15px;
    position: relative;
    padding-right: 15px;
    padding-left: 215px;
}

/* .users__container {
    padding: 25px 75px 75px 75px;
} */

.search__group {
    display: flex;
    margin: 0 auto;
    width: 315px;
    padding-top: 25px;
    margin-bottom: 30px;
}

.input__search {
    background-color: #202128;
    border-top-left-radius: 100px;
    border-bottom-left-radius: 100px;
    border-color: transparent;
    padding-left: 10px;
    color: #949494;
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #949494;
    height: 45px;
    outline: none;
}

.users__search {
    background-color: #202128;
    border-top-right-radius: 100px;
    border-bottom-right-radius: 100px;
    align-items: center;
    width: 35px;
    display: flex;
    justify-content: center;
    color: #949494;
    font-size: 21px;
    cursor: pointer;
    height: 45px;
}

.users__list {
    display: flex;
    gap: 20px;
    margin: 0 auto;
    padding: 0 70px;
    flex-direction: column;
}

.users__list.loading {
    opacity: .3;
}

.users__container {
    padding-bottom: 30px;
}

.admin__user {
    display: flex;
    align-items: center;;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    border-radius: 20px;
    padding: 10px 30px;
    margin: 0 auto;
}

.admin__user_info {
    display: flex;
    gap: 10px;
    align-items: center;
    border-right: 1px solid #383838;
    height: 100%;
    padding: 0 15px;
}

.admin__user_info img {
    width: 50px;
    height: 50px;
    border-radius: 100px;
}

.admin__user_info span {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
}

.admin__user_ip {
    align-items: center;
    display: flex;
    padding: 0 15px;
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    border-right: 1px solid #383838;
    height: 100%;
}

.admin__user_balance {
    align-items: center;
    display: flex;
    padding: 0 15px;
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    border-right: 1px solid #383838;
    height: 100%;
}

.admin__user_block {
    align-items: center;
    display: flex;
    padding: 0 15px;
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    border-right: 1px solid #383838;
    height: 100%;
}

.admin__user_edit {
    align-items: center;
    display: flex;
    padding: 0 15px;
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    height: 100%;
}

.admin__user_button {
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    padding: 12px 12px;
}

.manage__username {
    display: flex;
    align-items: center;
    gap: 25px;
    padding: 70px;
}

.manage__username .user__avatar_mg {
    width: 190px;
    height: 190px;
    border-radius: 100px;
}

.manage__personal_info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.username__personal {
    font-style: normal;
    font-weight: 500;
    font-size: 32px;
    line-height: 48px;
    color: #FFFFFF;
}

.id__personal {
    display: flex;
}

.manage__copy_id {
    height: 50px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    width: 50px;
    color: var(--main-color);
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.input__user_id {
    padding-left: 15px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    width: 150px;
    height: 46px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    outline: 0;
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 29px;
    color: #4E4E4E;
    border-color: transparent;
}

.user__personal_securite {
    display: flex;
    gap: 40px;
    flex-direction: column;
    margin-left: 50px;
}

.user__rank {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 0 auto;
}

.user__rank_icon {
    position: absolute;
    border: none;
    width: 120px;
    left: 170px;
    top: 95px;
}

.withdraw__user_image_rank {
    position: absolute;
    border: none;
    width: 60px;
    left: 20px;
    top: -20px;
}

.user__manipulation {
    display: flex;
    gap: 15px;
}

.user__balance {
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    border-radius: 10px;
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    outline: 0;
    width: 100%;
    height: 46px;
    border-color: transparent;
    padding-left: 15px;
}

.user__banned {
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    border-radius: 10px;
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    outline: 0;
    width: 100%;
    height: 50px;
    border-color: transparent;
    padding-left: 15px;
    margin-bottom: 10px;
}

.user__ip {
    padding-left: 15px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    border-radius: 10px;
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    outline: 0;
    width: 100%;
    height: 46px;
    border-color: transparent;
}

.progress__bar_bet {
    position: relative;
    width: 400px;
    height: 25px;
}

.progress__bar {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 9999px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.141) -164.91%, rgba(217, 217, 217, 0.024) -60.22%);
    backdrop-filter: blur(250px);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress__bar span {
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    padding: 0 10px;
    z-index: 2;
}

.progress__bar_pay {
    position: relative;
    width: 400px;
    height: 25px;
}

.success__bar {
    z-index: 1;
    --tw-ring-offset-shadow: 0 0 transparent;
    --tw-ring-shadow: 0 0 transparent;
    position: absolute;
    height: 100%;
    border-radius: 9999px;
    background-color: #189e58;
    box-shadow: var(--tw-ring-offset-shadow,0 0 transparent),var(--tw-ring-shadow,0 0 transparent),0 6px 16px rgba(51,193,108,0.25);
}

.user__withdraws {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    margin-bottom: 10px;
}

.user__games {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    margin-bottom: 10px;
}

.user__payments {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    margin-bottom: 10px;
}

.user__promocodes {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    margin-bottom: 10px;
}

.user__referrals {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    margin-bottom: 10px;
}

.user__multiaccounts {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 25px;
    padding-bottom: 10px;
}

.table__user {
    width: 100%;
    --table-cols: 20% 20% 20% 20% 20%;
    border-collapse: collapse;
}

.table__crash {
    width: 100%;
    --table-cols: 10% 15% 15% 15% 15% 15% 15%;
    border-collapse: collapse;
}

.table__statistic {
    width: 100%;
    --table-cols: 33% 33% 33%;
    border-collapse: collapse;
}

.user__withdraw {
    --table-cols: 16% 16% 16% 16% 16% 16%;
}

.user__promocodes_table {
    --table-cols: 25% 25% 25% 25%;
}

.user__referrals_table {
    --table-cols: 25% 25% 25% 25%;
}

.user__game {
    --table-cols: 20% 20% 20% 20% 20%;
}

.multiacc__table {
    --table-cols: 16% 16% 16% 16% 16% 16%;
}

.table__promocodes {
    width: 100%;
    --table-cols: 10% 15% 13% 13% 13% 17% 17%;
    border-collapse: collapse;
    margin-top: 30px;
}

.table__user thead {
    border-bottom: 1px solid var(--color-main-bg);
}

.table__user>thead {
    display: grid;
    height: 56px;
    align-content: center;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
}

.table__user>thead>tr {
    display: grid;
    grid-template-columns: var(--table-cols);
    justify-content: start;
    justify-items: start;
    color: #FFFFFF;
    padding: 0 20px;
}

.table__user>thead>tr>td {
    width: 100%;
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 29px;
    color: #FFFFFF;
}

.tb-center {
    text-align: center;
}

.tb-right {
    text-align: right;
}

.table__user tbody {
    overflow: hidden;
    position: relative;
    display: block;
}

.table__user>tbody>tr {
    height: 65px;
}

.table__user>tbody>tr:nth-child(2), .table__user>tbody>tr:nth-child(4), .table__user>tbody>tr:nth-child(6), .table__user>tbody>tr:nth-child(8), .table__user>tbody>tr:nth-child(10) {
    border-bottom: 1px solid var(--color-main-bg);
}

.table__user>tbody>tr {
    display: grid;
    grid-template-columns: var(--table-cols);
    height: 65px;
    align-content: center;
    align-items: center;
    animation-fill-mode: forwards;
    animation-duration: .5s;
    animation-timing-function: ease-out;
    will-change: transform,opacity;
    padding: 0 20px;
    border-bottom: 1px solid var(--color-main-bg);
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
}

.table__user tbody>tr>td {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 29px;
    color: #FFFFFF;
}

.table__user tbody>tr>td>a {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 29px;
    color: var(--main-color);
}

.name__table {
    font-style: normal;
    font-weight: 500;
    font-size: 21px;
    color: #FFFFFF;
    margin-bottom: 15px;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.statistic__inner {
    background: #1B1C24;
    border-radius: 15px;
    padding: 30px;
}

.promocodes__inner {
    background: #1B1C24;
    border-radius: 15px;
    padding: 50px;
}

.statistic__payments {
    display: flex;
    justify-content: space-around;
    margin-bottom: 50px;
}

.statitic__payments__header {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 21px;
    color: #FFFFFF;
}

.statistic__payments_amount {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 28px;
    color: #FFFFFF;
}

.site__info_users {
    display: flex;
    justify-content: space-around;
    margin-bottom: 50px;
}

.statistics__money {
    display: flex;
    gap: 15px;
}

.statistics__recent {
    width: 50%;
}

.promocodes__create {
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    color: #FFFFFF;
    padding: 8px 12px;
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    cursor: pointer;
}

.modal__window {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    overflow: hidden;
    outline: 0;
    visibility: hidden;
    opacity: 0;
    transition: .2s all ease;
    overflow-x: hidden;
    overflow-y: hidden;
}

.modal__promocodes {
    background: #16171D;
    border-radius: 15px;
    padding: 35px;
    max-width: 450px;
    margin: 75px auto;
}

.promocodes__create_head {
    font-style: normal;
    font-weight: 500;
    font-size: 26px;
    color: #FFFFFF;
    margin-bottom: 30px;
}

.promocodes__inputs {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.promocodes__input {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.promocodes__label_name {
    font-style: normal;
    font-weight: 400;
    font-size: 21px;
    line-height: 33px;
    color: #FFFFFF;
}

.promocodes__input_code {
    width: 300px;
    height: 40px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
    backdrop-filter: blur(250px);
    border-radius: 8px;
    border-color: transparent;
    outline: 0;
    color: #FFFFFF;
    font-weight: 400;
    font-size: 16px;
    padding-left: 10px;
}

.promocodes__select {
    width: 300px;
    height: 40px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
    backdrop-filter: blur(250px);
    border-radius: 8px;
    border-color: transparent;
    color: #FFFFFF;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    outline: 0;
}

.promocodes__select option {
    background-color: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
}

.promocodes__bottom_btn {
    margin-top: 25px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.promocodes__bottom_btn button {
    width: 49%;
}

.promocodes__create__btn {
    width: 100%;
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    color: #FFFFFF;
    border-color: transparent;
    font-style: normal;
    font-weight: 500;
    font-size: 21px;
    padding: 8px 0;
    text-align: center;
    cursor: pointer;
}

.promocodes__cancel {
    width: 100%;
    background: #D73400;
    border-radius: 10px;
    border-radius: 10px;
    color: #FFFFFF;
    font-style: normal;
    text-align: center;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    padding: 8px 0;
    cursor: pointer;
    border-color: transparent;
}

.modal__window.active {
    visibility: visible;
    opacity: 1;
    background: rgba(217, 217, 217, 0.01);
    backdrop-filter: blur(5px);
}

.settings__head {
    font-style: normal;
    font-weight: 500;
    font-size: 32px;
    color: #FFFFFF;
    margin-bottom: 35px;
}

.settings__inputs {
    display: flex;
    gap: 50px;
}

.settings__input {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.settings__input label{
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    color: #FFFFFF;
}

.settings__input input{
    width: 300px;
    height: 40px;
    font-style: normal;
    font-weight: 500;
    font-size: 21px;
    line-height: 29px;
    color: #FFFFFF;
    border-radius: 5px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
    backdrop-filter: blur(250px);
    outline: 0;
    border: none;
    border-color: transparent;
    padding-left: 5px;
}

.settings__site {
    background-color: #1B1C24;
    border-radius: 15px;
    padding: 40px;
}

.settings__games {
    margin-top: 10px;
    background-color: #1B1C24;
    border-radius: 15px;
    padding: 40px;
}

.reboot__games {
    margin-top: 10px;
    background-color: #1B1C24;
    border-radius: 15px;
    padding: 40px;
}

.setting__game {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
    border-radius: 5px;
    margin-top: 10px;
}

.setting__game_name {
    font-weight: 500;
    font-size: 18px;
    color: #FFFFFF;
}

.notification {
    right: 0;
    bottom: 0;
    position: fixed;
}

.notification-wrapper {
    display: block;
    overflow: hidden;
    width: 100%;
    margin: 0;
    padding: 0;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 15px;
    border-radius: 6px;
}

.notification-wrapper.error {
    background-color: rgba(216,51,51,.7);
    text-align: center;
    margin-bottom: 5px;
}

.notification-wrapper.success {
    background-color: rgba(22, 145, 81, .7);
    text-align: center;
    margin-bottom: 5px;
}

.notification-wrapper.error i {
    font-size: 22px;
    vertical-align: middle;
}

.user__container {
    background: none;
}

.notification-wrapper.success i {
    font-size: 22px;
    vertical-align: middle;
}

.user__manage_info {
    background-color: var(--color-inner);
    border-radius: 15px;
    margin-bottom: 10px;
}

.manage__user__save {
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    color: #FFFFFF;
    padding: 10px 15px;
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    border-color: transparent;
    width: 100%;
    cursor: pointer;
}

.user__manipulation {
    margin-bottom: 10px;
}

.manage__user_fast_info {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.manage__user_label {
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
    padding-left: 10px;
    margin-bottom: 5px;
}

.select__setting {
    width: 300px;
    height: 40px;
    font-style: normal;
    font-weight: 500;
    font-size: 21px;
    line-height: 29px;
    color: #FFFFFF;
    border-radius: 5px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.235) -164.91%, rgba(217, 217, 217, 0.04) -60.22%);
    backdrop-filter: blur(250px);
    outline: 0;
    border-color: transparent;
    padding-left: 5px;
}

.game__toggle_btn {
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
}

.game__toggle_btn input[type=checkbox] {
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
}

.toggle__switch_game {
    display: inline-block;
    height: 20px;
    border-radius: 14px;
    width: 40px;
    background: #FFFFFF;
    position: relative;
    margin-left: 10px;
    transition: all .25s;
}

.toggle__switch:before {
    background: var(--color-gray);
    opacity: 0;
}

.toggle__switch_game:after {
    background: #383838;
}

.toggle__switch_game:after, .toggle__switch_game:before {
    content: "";
    position: absolute;
    display: block;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    left: 3px;
    top: 50%;
    transform: translateY(-50%) translateX(0);
    transition: all .25s cubic-bezier(.5,-.6,.5,1.6);
}

.active .toggle__switch_game {
    background: var(--main-color);
}

.active .toggle__switch_game:after {
    left: 22px;
    background: #fff;
}

.save__settings {
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    margin-top: 15px;
    outline: none;
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    border-color: transparent;
    padding: 8px 12px;
}

.reboot__btn_game {
    background: linear-gradient(177.2deg, #2DD700 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    outline: none;
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    border-color: transparent;
    padding: 8px 12px;
}

.reboot__btns_game {
    display: flex;
    gap: 5px;
}

.select__games {
    display: flex;
    gap: 0.75rem;
    width: 100%;
    background: #22252F;
    margin-bottom: 10px;
    padding: 0.75rem;
    border-radius: 15px;
}

.select__game.active {
    background-color: #353746;
}

.select__game {
    color: #FFFFFF;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 10px 15px;
    background-color: #282a35;
    border-radius: .75rem;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color .2s ease-out 0s,filter .2s ease-out 0s;
}

.user__game {
    display: none;
}

.user__game.active {
    display: block;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.pagination li, .pagination li>a {
    font-weight: 500;
    font-size: 21px;
    color: #FFFFFF;
    border-radius: 5px;
    background: #22252F;
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    margin-right: 5px;
    align-items: center;
    text-align: center;
}

.pagination li.active {
    color: var(--main-color);
}

.pc__menu {
    display: block;
}

.mobile_menu {
    display: none;
    height: 55px;
    position: fixed;
    bottom: 0;
    width: 100%;
    background: var(--color-inner);
    z-index: 100;
}

.mobile_menu__content {
    position: relative;
    height: 100%;
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 5px;
    margin: auto;
    font-size: 11px;
    color: #7E7E7E;
}

.mobile_menu__link {
    display: block;
    width: 100%;
    text-align: center;
    padding: 5px 0;
    color: #7E7E7E;
}

.mobile_menu__link i {
    display: block;
    margin-bottom: 5px;
    font-size: 22px;
}

.withdraws__body {
    background: var(--color-inner);
    width: 100%;
    border-radius: 7px;
    margin-left: 8px;
    min-width: 0;
    align-self: stretch;
}

.withdraws__wt {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
}

.withdraw__wrapper {
    width: 50%;
    padding: 5px;
}

.withdraw__card {
    color: #cdcdcd;
    position: relative;
}

.withdraw__card_topside {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #21222A;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    position: relative;
}

.withdraw__card_time {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 11px;
}

.withdraw__time_text {
    font-weight: 500;
    font-size: 11px;
}

.withdraw__user_image_wr  {
    position: relative;
}

.withdraw__user_image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    padding: 4px;
    border: 1px solid #fff;
    margin-right: 10px;
}

.withdraw__user_username {
    font-size: 13px;
    color: #fff;
    font-weight: 500;
}

.withdraw__sum {
    font-size: 15px;
    color: #fff;
    font-weight: 700;
    margin-top: 5px;
}

.withdraw__section {
    background: #21222A;
    border-top: 1px solid #333333;
    padding: 12px;
    font-size: 13px;
    line-height: 1.45;
}

.text-bold {
    font-weight: 700;
    color: #fff;
}

.withdraw__link {
    text-decoration: none;
    color: var(--main-color);
    border-bottom: 1px dashed var(--main-color);
    transition: .2s ease;
}

.withdraw__multi {
    display: flex;
    flex-wrap: wrap;
    margin-top: 7px;
    max-height: 300px;
    overflow: auto;
}

.withdraw__multi_show {
    cursor: pointer;
    padding: 5px 12px;
    background: var(--main-color);
    font-weight: 500;
    color: #fff;
    font-size: 13px;
    display: block;
    margin-top: 7px;
    border-radius: 3px;
}

.withdraw__button_side {
    background: #21222A;
    padding: 3px 10px 15px 10px;
    display: flex;
    flex-wrap: wrap;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.withdraw__confirm {
    display: block;
    padding: 12px 15px;
    margin: 12px 5px 0;
    border-radius: 8px;
    font-size: 15px;
    flex: 1;
    cursor: pointer;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    text-align: center;
    font-weight: 500;
    background: rgb(22, 145, 81);;
    color: #fff;
    border-color: rgb(22, 145, 81);;
}

.withdraw__confirm:disabled {
    opacity: .7;
}

.withdraw__process_cancel {
    background: rgb(216,51,51);
    color: #fff;
    border-color: rgb(216,51,51);
}

.withdraw__multi_item {
    width: 100%;
    font-size: 14px;
}

.withdraw__multi_item a {
    color: #fff;
}

.aw_green {
    color: #6CC100;
}

.new__users {
    font-size: 14px;
    font-weight: 500;
}

@media (max-width: 500px) {
    .pc__menu {
        display: none;
    }
    .admin__content {
        padding-left: 15px;
    }
    .mobile_menu {
        display: block;
    }
    .settings__inputs {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    .users__list {
        gap: 10px;
        padding: 0 15px;
        overflow: auto;
    }
    .statistic__payments {
        margin-bottom: 25px;
        gap: 20px;
        flex-direction: column;
    }
    .site__info_users {
        flex-direction: column;
        margin-bottom: 25px;
    }
    .statistics__money {
        flex-direction: column;
    }
    .withdraws__wt {
        flex-direction: column;
    }
    .withdraw__wrapper {
        width: 100%;
    }
    .statistics__recent {
        width: 100%;
    }
    .manage__username {
        flex-direction: column;
    }
    .user__rank_icon {
        position: absolute;
        border: none;
        width: 120px;
        left: 190px;
        top: 40px;
    }
    .user__personal_securite {
        margin-left: 0;
    }
    .select__games {
        overflow: auto;
    }
    .pagination {
        overflow: auto;
    }
    .user__payments_table {
        overflow: auto;
    }
}