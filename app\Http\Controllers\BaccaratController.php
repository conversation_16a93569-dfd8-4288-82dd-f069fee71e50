<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Baccarat;
use App\Models\User;
use App\Models\Settings;
use App\Models\Profit;

class BaccaratController extends Controller
{
    /**
     * Play a baccarat hand
     */
    public function play(Request $request)
    {
        $settings = Settings::where('id', 1)->first();
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }
        
        if ($settings && isset($settings->baccarat_enabled) && $settings->baccarat_enabled == 1) {
            return response()->json(['type' => 'error', 'msg' => 'В данный момент режим недоступен!']);
        }

        $bet = $request->bet;
        $choice = $request->choice; // 'player', 'banker', or 'tie'
        $user = Auth::user();

        // Validation
        if ($bet < 1 || !is_numeric($bet)) {
            return response()->json(['type' => 'error', 'msg' => 'Минимальная сумма ставки - 1 рубль!']);
        }
        
        if ($bet > $user->balance) {
            return response()->json(['type' => 'error', 'msg' => 'Недостаточно средств']);
        }

        if (!in_array($choice, ['player', 'banker', 'tie'])) {
            return response()->json(['type' => 'error', 'msg' => 'Неверный выбор ставки']);
        }

        DB::beginTransaction();
        try {
            // Deduct bet from user balance
            $user->balance -= $bet;
            $user->save();

            // Generate deck and deal cards
            $deck = Baccarat::generateDeck();
            
            // Deal initial two cards to each hand
            $playerCards = [array_pop($deck), array_pop($deck)];
            $bankerCards = [array_pop($deck), array_pop($deck)];
            
            $playerTotal = Baccarat::calculateHandTotal($playerCards);
            $bankerTotal = Baccarat::calculateHandTotal($bankerCards);
            
            // Check for natural (8 or 9)
            $playerNatural = ($playerTotal >= 8);
            $bankerNatural = ($bankerTotal >= 8);
            
            $playerThirdCard = null;
            $bankerThirdCard = null;
            
            // If no natural, apply drawing rules
            if (!$playerNatural && !$bankerNatural) {
                // Player draws third card if needed
                if (Baccarat::playerNeedsThirdCard($playerTotal)) {
                    $playerThirdCard = array_pop($deck);
                    $playerCards[] = $playerThirdCard;
                    $playerTotal = Baccarat::calculateHandTotal($playerCards);
                    $playerThirdCardValue = Baccarat::getCardValue($playerThirdCard);
                } else {
                    $playerThirdCardValue = null;
                }
                
                // Banker draws third card based on rules
                if (Baccarat::bankerNeedsThirdCard($bankerTotal, $playerTotal, $playerThirdCardValue)) {
                    $bankerThirdCard = array_pop($deck);
                    $bankerCards[] = $bankerThirdCard;
                    $bankerTotal = Baccarat::calculateHandTotal($bankerCards);
                }
            }

            // Determine winner
            $winner = Baccarat::determineWinner($playerTotal, $bankerTotal);
            $isWin = ($choice === $winner);
            $payoutMultiplier = Baccarat::getPayoutMultiplier($choice);
            $winAmount = 0;

            if ($isWin) {
                $winAmount = $bet * ($payoutMultiplier + 1); // Include original bet
                $user->balance += $winAmount;
                $user->save();
                $this->recordProfit(-($winAmount - $bet)); // House loses
            } else {
                $this->recordProfit($bet); // House wins
            }

            // Create game record
            $game = Baccarat::create([
                'user_id' => $user->id,
                'bet' => $bet,
                'choice' => $choice,
                'player_cards' => $playerCards,
                'banker_cards' => $bankerCards,
                'player_total' => $playerTotal,
                'banker_total' => $bankerTotal,
                'winner' => $winner,
                'status' => $isWin ? 'win' : 'lose',
                'win' => $winAmount,
                'payout_multiplier' => $payoutMultiplier
            ]);

            DB::commit();

            return response()->json([
                'type' => 'success',
                'game' => $game,
                'player_cards' => $playerCards,
                'banker_cards' => $bankerCards,
                'player_total' => $playerTotal,
                'banker_total' => $bankerTotal,
                'winner' => $winner,
                'choice' => $choice,
                'is_win' => $isWin,
                'win_amount' => $winAmount,
                'balance' => $user->balance,
                'winner_display' => Baccarat::getDisplayName($winner),
                'choice_display' => Baccarat::getDisplayName($choice),
                'player_third_card' => $playerThirdCard,
                'banker_third_card' => $bankerThirdCard
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['type' => 'error', 'msg' => 'Ошибка при игре']);
        }
    }

    /**
     * Get game history
     */
    public function history(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }

        $history = Baccarat::where('user_id', Auth::user()->id)
                          ->orderBy('id', 'desc')
                          ->limit(20)
                          ->get();

        $historyWithDisplay = $history->map(function ($game) {
            return [
                'id' => $game->id,
                'bet' => $game->bet,
                'choice' => $game->choice,
                'winner' => $game->winner,
                'status' => $game->status,
                'win' => $game->win,
                'player_total' => $game->player_total,
                'banker_total' => $game->banker_total,
                'choice_display' => Baccarat::getDisplayName($game->choice),
                'winner_display' => Baccarat::getDisplayName($game->winner),
                'created_at' => $game->created_at
            ];
        });

        return response()->json([
            'type' => 'success',
            'history' => $historyWithDisplay
        ]);
    }

    /**
     * Get recent global results
     */
    public function recentResults(Request $request)
    {
        $results = Baccarat::orderBy('id', 'desc')
                          ->limit(50)
                          ->get(['winner', 'player_total', 'banker_total', 'created_at']);

        return response()->json([
            'type' => 'success',
            'results' => $results
        ]);
    }

    /**
     * Record profit for the house
     */
    private function recordProfit($amount)
    {
        Profit::create([
            'game' => 'baccarat',
            'sum' => $amount
        ]);
    }
}
