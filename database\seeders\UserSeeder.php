<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert([
            'id' => 1,
            'username' => 'demo_user',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'balance' => 10000.00,
            'wager' => 0.00,
            'avatar' => 'default.png',
            'admin' => 0,
            'youtuber' => 0,
            'ban' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create initial wheel game
        DB::table('wheel')->insert([
            'id' => 1,
            'status' => 0,
            'winner_color' => null,
            'winner_number' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create initial crash game
        DB::table('crash')->insert([
            'id' => 1,
            'multiplier' => 1.0000,
            'status' => 'waiting',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create initial jackpot game
        DB::table('jackpot')->insert([
            'id' => 1,
            'total_amount' => 0.00,
            'winner_id' => null,
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
