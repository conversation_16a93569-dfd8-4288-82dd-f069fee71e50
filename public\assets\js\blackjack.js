$(document).ready(function() {
    let currentGame = null;
    
    // Start game button
    $('#start-btn').click(function() {
        const bet = parseFloat($('.input__bet').val());
        
        if (!bet || bet < 1) {
            showStatus('Enter a valid bet amount', 'error');
            return;
        }
        
        startGame(bet);
    });
    
    // Game action buttons
    $('#hit-btn').click(() => hit());
    $('#stand-btn').click(() => stand());
    $('#double-btn').click(() => double());
    $('#split-btn').click(() => split());
    
    // Bet adjustment functions
    window.adjustBet = function(amount) {
        const currentBet = parseFloat($('.input__bet').val()) || 0;
        $('.input__bet').val(Math.max(1, currentBet + amount));
    };
    
    window.setBet = function(type) {
        if (type === 'min') {
            $('.input__bet').val(1);
        } else if (type === 'max') {
            const balance = parseFloat($('#balance').text()) || 0;
            $('.input__bet').val(balance);
        }
    };
    
    function startGame(bet) {
        $.post('/blackjack/start', {
            _token: $('meta[name="csrf-token"]').attr('content'),
            bet: bet
        }).then(response => {
            if (response.type === 'success') {
                currentGame = response.game;
                updateGameDisplay(response.game);
                updateBalance(response.balance);
                
                if (response.game.status === 'playing') {
                    showGameActions();
                    showStatus('Your turn! Hit or Stand', 'info');
                } else {
                    showGameResult(response.game);
                }
                
                // Show only dealer's first card
                displayDealerCards([response.dealer_visible_card], true);
                displayPlayerCards(response.game.player_cards);
            } else {
                showStatus(response.msg, 'error');
            }
        }).catch(error => {
            showStatus('Error creating game', 'error');
        });
    }
    
    function hit() {
        if (!currentGame) return;
        
        $.post('/blackjack/hit', {
            _token: $('meta[name="csrf-token"]').attr('content')
        }).then(response => {
            if (response.type === 'success') {
                currentGame = response.game;
                updateGameDisplay(response.game);
                displayPlayerCards(response.game.player_cards);
                
                if (response.game.status === 'bust') {
                    showGameResult(response.game);
                    hideGameActions();
                } else {
                    updateActionButtons(response.game);
                }
            } else {
                showStatus(response.msg, 'error');
            }
        });
    }
    
    function stand() {
        if (!currentGame) return;
        
        $.post('/blackjack/stand', {
            _token: $('meta[name="csrf-token"]').attr('content')
        }).then(response => {
            if (response.type === 'success') {
                currentGame = response.game;
                updateGameDisplay(response.game);
                displayDealerCards(response.game.dealer_cards, false);
                displayPlayerCards(response.game.player_cards);
                showGameResult(response.game);
                updateBalance(response.balance);
                hideGameActions();
            } else {
                showStatus(response.msg, 'error');
            }
        });
    }
    
    function updateGameDisplay(game) {
        $('#player-total').text(game.player_total);
        $('#dealer-total').text(game.status === 'playing' ? '?' : game.dealer_total);
        updateActionButtons(game);
    }
    
    function updateActionButtons(game) {
        $('#double-btn').toggle(game.can_double && game.status === 'playing');
        $('#split-btn').toggle(game.can_split && game.status === 'playing');
        
        if (game.status !== 'playing') {
            hideGameActions();
        }
    }
    
    function displayPlayerCards(cards) {
        const container = $('#player-cards');
        container.empty();
        
        cards.forEach(card => {
            container.append(createCardElement(card));
        });
    }
    
    function displayDealerCards(cards, hideSecond = false) {
        const container = $('#dealer-cards');
        container.empty();
        
        cards.forEach((card, index) => {
            if (hideSecond && index === 1) {
                container.append(createCardBack());
            } else {
                container.append(createCardElement(card));
            }
        });
    }
    
    function createCardElement(card) {
        const isRed = card.suit === 'hearts' || card.suit === 'diamonds';
        const suitSymbol = getSuitSymbol(card.suit);
        
        return `
            <div class="blackjack__card ${isRed ? 'red' : 'black'}">
                <div class="blackjack__card_rank">${card.rank}</div>
                <div class="blackjack__card_suit">${suitSymbol}</div>
                <div class="blackjack__card_rank" style="transform: rotate(180deg);">${card.rank}</div>
            </div>
        `;
    }
    
    function createCardBack() {
        return `
            <div class="blackjack__card blackjack__card_back">
                <div>?</div>
            </div>
        `;
    }
    
    function getSuitSymbol(suit) {
        const symbols = {
            'hearts': '♥',
            'diamonds': '♦',
            'clubs': '♣',
            'spades': '♠'
        };
        return symbols[suit] || suit;
    }
    
    function showGameActions() {
        $('#game-actions').show();
        $('#start-btn').hide();
    }
    
    function hideGameActions() {
        $('#game-actions').hide();
        $('#start-btn').show();
        currentGame = null;
    }
    
    function showGameResult(game) {
        let message = '';
        let className = '';
        
        switch (game.status) {
            case 'player_win':
                message = `You won $${game.win.toFixed(2)}!`;
                className = 'win';
                break;
            case 'dealer_win':
                message = 'Dealer wins';
                className = 'lose';
                break;
            case 'push':
                message = 'Push! Bet returned';
                className = 'push';
                break;
            case 'blackjack':
                message = `Blackjack! You won $${game.win.toFixed(2)}!`;
                className = 'win';
                break;
            case 'bust':
                message = 'Bust! You lose';
                className = 'lose';
                break;
        }
        
        showStatus(message, className);
        addToHistory(game, message, className);
    }
    
    function showStatus(message, type = 'info') {
        const statusElement = $('#game-status .blackjack__status_text');
        statusElement.text(message);
        
        const statusContainer = $('#game-status');
        statusContainer.removeClass('win lose push error info').addClass(type);
    }
    
    function addToHistory(game, message, className) {
        const historyItem = `
            <div class="blackjack__history_item ${className}">
                <span>Bet: $${game.bet}</span> -
                <span>${message}</span>
                <span style="float: right;">${new Date().toLocaleTimeString()}</span>
            </div>
        `;
        
        $('#game-history').prepend(historyItem);
        
        // Keep only last 10 items
        $('#game-history .blackjack__history_item').slice(10).remove();
    }
    
    function updateBalance(newBalance) {
        $('#balance').text(newBalance.toFixed(2));
    }
    
    // Initialize
    showStatus('Place your bet to start the game', 'info');
});
