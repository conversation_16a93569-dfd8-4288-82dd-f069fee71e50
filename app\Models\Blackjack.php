<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blackjack extends Model
{
    use HasFactory;

    protected $table = 'blackjack';

    protected $fillable = [
        'user_id', 'bet', 'player_cards', 'dealer_cards', 
        'player_total', 'dealer_total', 'status', 'win',
        'can_double', 'can_split'
    ];

    protected $casts = [
        'player_cards' => 'array',
        'dealer_cards' => 'array',
        'can_double' => 'boolean',
        'can_split' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate hand total with proper Ace handling
     */
    public static function calculateHandTotal($cards)
    {
        $total = 0;
        $aces = 0;

        foreach ($cards as $card) {
            $value = self::getCardValue($card);
            if ($value == 11) {
                $aces++;
            }
            $total += $value;
        }

        // Adjust for Aces
        while ($total > 21 && $aces > 0) {
            $total -= 10;
            $aces--;
        }

        return $total;
    }

    /**
     * Get card value for blackjack
     */
    public static function getCardValue($card)
    {
        $rank = $card['rank'];
        
        if (in_array($rank, ['J', 'Q', 'K'])) {
            return 10;
        } elseif ($rank == 'A') {
            return 11; // Will be adjusted in calculateHandTotal if needed
        } else {
            return (int) $rank;
        }
    }

    /**
     * Check if hand is blackjack (21 with 2 cards)
     */
    public function isBlackjack($cards)
    {
        return count($cards) == 2 && self::calculateHandTotal($cards) == 21;
    }

    /**
     * Check if hand can be split
     */
    public function canSplit()
    {
        $cards = $this->player_cards;
        return count($cards) == 2 && 
               self::getCardValue($cards[0]) == self::getCardValue($cards[1]);
    }

    /**
     * Generate a new deck of cards
     */
    public static function generateDeck()
    {
        $suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        $ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        $deck = [];

        foreach ($suits as $suit) {
            foreach ($ranks as $rank) {
                $deck[] = ['suit' => $suit, 'rank' => $rank];
            }
        }

        shuffle($deck);
        return $deck;
    }
}
