<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wheel', function (Blueprint $table) {
            $table->id();
            $table->integer('status')->default(0); // 0=waiting, 1=spinning, 2=finished
            $table->string('winner_color')->nullable();
            $table->integer('winner_number')->nullable();
            $table->timestamps();
        });

        Schema::create('wheel_bets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('game_id');
            $table->bigInteger('user_id');
            $table->string('color');
            $table->decimal('price', 10, 2);
            $table->decimal('balance', 10, 2);
            $table->timestamps();
            
            $table->index(['game_id', 'user_id']);
            $table->index('color');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wheel_bets');
        Schema::dropIfExists('wheel');
    }
};
