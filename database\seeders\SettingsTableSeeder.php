<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('settings')->insert([
            'id' => 1,
            'site_name' => 'BrixCasino',
            'site_description' => 'A cutting-edge online gambling platform',
            'telegram_bot_token' => null,
            'telegram_channel' => null,
            'min_deposit' => 1.00,
            'min_withdraw' => 5.00,
            'max_withdraw' => 1000.00,
            'referral_bonus' => 0.50,
            'maintenance_mode' => '0',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
