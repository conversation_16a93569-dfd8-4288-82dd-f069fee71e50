<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Dice games table
        Schema::create('dice', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->integer('chance');
            $table->integer('roll');
            $table->decimal('win', 10, 2)->default(0);
            $table->enum('status', ['win', 'lose']);
            $table->timestamps();
            
            $table->index('user_id');
        });

        // Mines games table
        Schema::create('mines', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->integer('mines_count');
            $table->json('mines_positions');
            $table->json('opened_cells')->nullable();
            $table->decimal('current_multiplier', 8, 4)->default(1.0000);
            $table->decimal('win', 10, 2)->default(0);
            $table->enum('status', ['playing', 'win', 'lose', 'cashout']);
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
        });

        // Crash games table
        Schema::create('crash', function (Blueprint $table) {
            $table->id();
            $table->decimal('multiplier', 8, 4);
            $table->enum('status', ['waiting', 'playing', 'crashed']);
            $table->timestamps();
        });

        // Crash bets table
        Schema::create('crash_bets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('game_id');
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->decimal('cashout_at', 8, 4)->nullable();
            $table->decimal('win', 10, 2)->default(0);
            $table->enum('status', ['playing', 'win', 'lose']);
            $table->timestamps();
            
            $table->index(['game_id', 'user_id']);
        });

        // Jackpot games table
        Schema::create('jackpot', function (Blueprint $table) {
            $table->id();
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->bigInteger('winner_id')->nullable();
            $table->enum('status', ['active', 'drawing', 'finished']);
            $table->timestamps();
        });

        // Jackpot bets table
        Schema::create('jackpot_bets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('game_id');
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->decimal('chance', 8, 4);
            $table->timestamps();
            
            $table->index(['game_id', 'user_id']);
        });

        // Profit tracking table
        Schema::create('profit', function (Blueprint $table) {
            $table->id();
            $table->string('game');
            $table->decimal('sum', 10, 2);
            $table->timestamps();
            
            $table->index('game');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('profit');
        Schema::dropIfExists('jackpot_bets');
        Schema::dropIfExists('jackpot');
        Schema::dropIfExists('crash_bets');
        Schema::dropIfExists('crash');
        Schema::dropIfExists('mines');
        Schema::dropIfExists('dice');
    }
};
