@extends('layout')

@section('content')
<div class="blackjack__container">
    <div class="games__area">
        <div class="games__sidebar">
            <div class="games__input_wrapper_bet">
                <label class="games__sidebar_label">Ставка</label>
                <div class="games__sidebar_wrapper_input">
                    <input type="number" class="games__sidebar_input input__bet" value="10" min="1">
                </div>
                <div class="games__sidebar_help_bombs">
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(1)">+1</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(10)">+10</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(100)">+100</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('min')">Min</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('max')">Max</button>
                </div>
            </div>
            
            <div class="blackjack__actions" id="game-actions" style="display: none;">
                <button class="blackjack__btn blackjack__btn_hit" id="hit-btn">Взять карту</button>
                <button class="blackjack__btn blackjack__btn_stand" id="stand-btn">Остановиться</button>
                <button class="blackjack__btn blackjack__btn_double" id="double-btn" style="display: none;">Удвоить</button>
                <button class="blackjack__btn blackjack__btn_split" id="split-btn" style="display: none;">Разделить</button>
            </div>
            
            <div class="blackjack__start_wrapper">
                <button class="blackjack__start_btn" id="start-btn">Начать игру</button>
            </div>
        </div>
        
        <div class="blackjack__field">
            <div class="blackjack__table">
                <!-- Dealer Section -->
                <div class="blackjack__dealer">
                    <div class="blackjack__dealer_title">Дилер</div>
                    <div class="blackjack__dealer_total" id="dealer-total">?</div>
                    <div class="blackjack__dealer_cards" id="dealer-cards">
                        <!-- Dealer cards will be populated here -->
                    </div>
                </div>
                
                <!-- Game Status -->
                <div class="blackjack__status" id="game-status">
                    <div class="blackjack__status_text">Сделайте ставку для начала игры</div>
                </div>
                
                <!-- Player Section -->
                <div class="blackjack__player">
                    <div class="blackjack__player_title">Ваши карты</div>
                    <div class="blackjack__player_total" id="player-total">0</div>
                    <div class="blackjack__player_cards" id="player-cards">
                        <!-- Player cards will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game History -->
    <div class="blackjack__history" style="margin-top: 20px;">
        <div class="blackjack__history_title">История игр</div>
        <div class="blackjack__history_list" id="game-history">
            <!-- Game history will be populated here -->
        </div>
    </div>
    
    <script src="/assets/js/blackjack.js"></script>
</div>

<style>
.blackjack__container {
    padding: 20px;
}

.blackjack__field {
    flex: 1;
    margin-left: 20px;
}

.blackjack__table {
    background: linear-gradient(135deg, #1a4d3a, #2d5a3d);
    border-radius: 15px;
    padding: 30px;
    min-height: 500px;
    position: relative;
    border: 3px solid #4a7c59;
}

.blackjack__dealer, .blackjack__player {
    text-align: center;
    margin: 20px 0;
}

.blackjack__dealer_title, .blackjack__player_title {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10px;
}

.blackjack__dealer_total, .blackjack__player_total {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 15px;
}

.blackjack__dealer_cards, .blackjack__player_cards {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    min-height: 100px;
}

.blackjack__card {
    width: 60px;
    height: 84px;
    background: #fff;
    border-radius: 8px;
    border: 2px solid #333;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px;
    font-weight: bold;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.blackjack__card.red {
    color: #d32f2f;
}

.blackjack__card.black {
    color: #333;
}

.blackjack__card_back {
    background: linear-gradient(45deg, #1976d2, #42a5f5);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.blackjack__card_rank {
    font-size: 14px;
    line-height: 1;
}

.blackjack__card_suit {
    font-size: 18px;
    text-align: center;
}

.blackjack__status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 15px 25px;
    border-radius: 10px;
    text-align: center;
    min-width: 200px;
}

.blackjack__status_text {
    font-size: 16px;
    font-weight: bold;
}

.blackjack__actions {
    margin-top: 20px;
}

.blackjack__btn {
    display: block;
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.blackjack__btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
}

.blackjack__btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

.blackjack__start_btn {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.blackjack__start_btn:hover {
    background: linear-gradient(135deg, #f57c00, #ef6c00);
    transform: translateY(-2px);
}

.blackjack__history {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
}

.blackjack__history_title {
    color: #fff;
    font-weight: bold;
    margin-bottom: 10px;
}

.blackjack__history_item {
    background: rgba(255,255,255,0.1);
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
}

.win { color: #4caf50; }
.lose { color: #f44336; }
.push { color: #ff9800; }
</style>
@endsection
