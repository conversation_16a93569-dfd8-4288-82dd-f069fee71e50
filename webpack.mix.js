let mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// mix.js('resources/js/app.js', 'public/assets/js')
//     .js('resources/js/crash.js', 'public/assets/js')
//     .js('resources/js/dice.js', 'public/assets/js')
//     .js('resources/js/mines.js', 'public/assets/js')
//     .js('resources/js/socket.js', 'public/assets/js')
//     .js('resources/js/wallet.js', 'public/assets/js')
//     .version();
