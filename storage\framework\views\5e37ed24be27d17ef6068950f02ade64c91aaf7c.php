<?php $__env->startSection('content'); ?>
<div class="roulette__container">
    <div class="games__area">
        <div class="games__sidebar">
            <div class="games__input_wrapper_bet">
                <label class="games__sidebar_label">Bet Amount</label>
                <div class="games__sidebar_wrapper_input">
                    <input type="number" class="games__sidebar_input input__bet" value="10" min="1">
                </div>
                <div class="games__sidebar_help_bombs">
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(1)">+1</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(10)">+10</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(100)">+100</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('min')">Min</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('max')">Max</button>
                </div>
            </div>
            
            <!-- Quick Bet Buttons -->
            <div class="roulette__quick_bets">
                <h4>Quick Bets</h4>
                <button class="roulette__quick_btn red" onclick="placeBet('red', 'red')">Красное (1:1)</button>
                <button class="roulette__quick_btn black" onclick="placeBet('black', 'black')">Чёрное (1:1)</button>
                <button class="roulette__quick_btn even" onclick="placeBet('even', 'even')">Чётное (1:1)</button>
                <button class="roulette__quick_btn odd" onclick="placeBet('odd', 'odd')">Нечётное (1:1)</button>
                <button class="roulette__quick_btn low" onclick="placeBet('low', 'low')">1-18 (1:1)</button>
                <button class="roulette__quick_btn high" onclick="placeBet('high', 'high')">19-36 (1:1)</button>
            </div>

            <!-- User Bets -->
            <div class="roulette__user_bets">
                <h4>Ваши ставки</h4>
                <div id="user-bets-list">
                    <!-- User bets will be populated here -->
                </div>
                <div class="roulette__total_bet">
                    Общая ставка: <span id="total-user-bet">0</span> ₽
                </div>
            </div>
        </div>
        
        <div class="roulette__field">
            <!-- Roulette Wheel -->
            <div class="roulette__wheel_container">
                <div class="roulette__wheel" id="roulette-wheel">
                    <div class="roulette__ball" id="roulette-ball"></div>
                    <div class="roulette__center">
                        <div class="roulette__winning_number" id="winning-number">-</div>
                    </div>
                </div>
                <div class="roulette__status" id="game-status">
                    Делайте ваши ставки!
                </div>
            </div>
            
            <!-- Betting Table -->
            <div class="roulette__table">
                <!-- Numbers Grid -->
                <div class="roulette__numbers_grid">
                    <!-- Zero -->
                    <div class="roulette__number zero" data-number="0" onclick="placeBet('straight', '0')">0</div>
                    
                    <!-- Numbers 1-36 -->
                    <div class="roulette__numbers_main">
                        <!-- Row 1: 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36 -->
                        <div class="roulette__row">
                            <div class="roulette__number red" data-number="3" onclick="placeBet('straight', '3')">3</div>
                            <div class="roulette__number black" data-number="6" onclick="placeBet('straight', '6')">6</div>
                            <div class="roulette__number red" data-number="9" onclick="placeBet('straight', '9')">9</div>
                            <div class="roulette__number red" data-number="12" onclick="placeBet('straight', '12')">12</div>
                            <div class="roulette__number black" data-number="15" onclick="placeBet('straight', '15')">15</div>
                            <div class="roulette__number red" data-number="18" onclick="placeBet('straight', '18')">18</div>
                            <div class="roulette__number red" data-number="21" onclick="placeBet('straight', '21')">21</div>
                            <div class="roulette__number black" data-number="24" onclick="placeBet('straight', '24')">24</div>
                            <div class="roulette__number red" data-number="27" onclick="placeBet('straight', '27')">27</div>
                            <div class="roulette__number red" data-number="30" onclick="placeBet('straight', '30')">30</div>
                            <div class="roulette__number black" data-number="33" onclick="placeBet('straight', '33')">33</div>
                            <div class="roulette__number red" data-number="36" onclick="placeBet('straight', '36')">36</div>
                        </div>
                        
                        <!-- Row 2: 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35 -->
                        <div class="roulette__row">
                            <div class="roulette__number black" data-number="2" onclick="placeBet('straight', '2')">2</div>
                            <div class="roulette__number red" data-number="5" onclick="placeBet('straight', '5')">5</div>
                            <div class="roulette__number black" data-number="8" onclick="placeBet('straight', '8')">8</div>
                            <div class="roulette__number black" data-number="11" onclick="placeBet('straight', '11')">11</div>
                            <div class="roulette__number red" data-number="14" onclick="placeBet('straight', '14')">14</div>
                            <div class="roulette__number black" data-number="17" onclick="placeBet('straight', '17')">17</div>
                            <div class="roulette__number black" data-number="20" onclick="placeBet('straight', '20')">20</div>
                            <div class="roulette__number red" data-number="23" onclick="placeBet('straight', '23')">23</div>
                            <div class="roulette__number black" data-number="26" onclick="placeBet('straight', '26')">26</div>
                            <div class="roulette__number black" data-number="29" onclick="placeBet('straight', '29')">29</div>
                            <div class="roulette__number red" data-number="32" onclick="placeBet('straight', '32')">32</div>
                            <div class="roulette__number black" data-number="35" onclick="placeBet('straight', '35')">35</div>
                        </div>
                        
                        <!-- Row 3: 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34 -->
                        <div class="roulette__row">
                            <div class="roulette__number red" data-number="1" onclick="placeBet('straight', '1')">1</div>
                            <div class="roulette__number black" data-number="4" onclick="placeBet('straight', '4')">4</div>
                            <div class="roulette__number red" data-number="7" onclick="placeBet('straight', '7')">7</div>
                            <div class="roulette__number black" data-number="10" onclick="placeBet('straight', '10')">10</div>
                            <div class="roulette__number black" data-number="13" onclick="placeBet('straight', '13')">13</div>
                            <div class="roulette__number red" data-number="16" onclick="placeBet('straight', '16')">16</div>
                            <div class="roulette__number red" data-number="19" onclick="placeBet('straight', '19')">19</div>
                            <div class="roulette__number black" data-number="22" onclick="placeBet('straight', '22')">22</div>
                            <div class="roulette__number red" data-number="25" onclick="placeBet('straight', '25')">25</div>
                            <div class="roulette__number black" data-number="28" onclick="placeBet('straight', '28')">28</div>
                            <div class="roulette__number black" data-number="31" onclick="placeBet('straight', '31')">31</div>
                            <div class="roulette__number red" data-number="34" onclick="placeBet('straight', '34')">34</div>
                        </div>
                    </div>
                </div>
                
                <!-- Dozen Bets -->
                <div class="roulette__dozens">
                    <div class="roulette__dozen" onclick="placeBet('first_dozen', '1-12')">1-я дюжина (2:1)</div>
                    <div class="roulette__dozen" onclick="placeBet('second_dozen', '13-24')">2-я дюжина (2:1)</div>
                    <div class="roulette__dozen" onclick="placeBet('third_dozen', '25-36')">3-я дюжина (2:1)</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game History -->
    <div class="roulette__history">
        <h4>История игр</h4>
        <div class="roulette__history_numbers" id="game-history">
            <!-- History will be populated here -->
        </div>
    </div>
    
    <script src="/assets/js/roulette.js"></script>
</div>

<style>
.roulette__container {
    padding: 20px;
}

.roulette__field {
    flex: 1;
    margin-left: 20px;
}

.roulette__wheel_container {
    text-align: center;
    margin-bottom: 30px;
}

.roulette__wheel {
    width: 200px;
    height: 200px;
    border: 8px solid #8B4513;
    border-radius: 50%;
    margin: 0 auto 20px;
    position: relative;
    background: conic-gradient(
        #ff0000 0deg 9.73deg,
        #000000 9.73deg 19.46deg,
        #ff0000 19.46deg 29.19deg,
        #000000 29.19deg 38.92deg,
        #ff0000 38.92deg 48.65deg,
        #000000 48.65deg 58.38deg,
        #ff0000 58.38deg 68.11deg,
        #000000 68.11deg 77.84deg,
        #ff0000 77.84deg 87.57deg,
        #000000 87.57deg 97.3deg,
        #008000 97.3deg 107.03deg,
        #ff0000 107.03deg 116.76deg,
        #000000 116.76deg 126.49deg,
        #ff0000 126.49deg 136.22deg,
        #000000 136.22deg 145.95deg,
        #ff0000 145.95deg 155.68deg,
        #000000 155.68deg 165.41deg,
        #ff0000 165.41deg 175.14deg,
        #000000 175.14deg 184.87deg,
        #ff0000 184.87deg 194.6deg,
        #000000 194.6deg 204.33deg,
        #ff0000 204.33deg 214.06deg,
        #000000 214.06deg 223.79deg,
        #ff0000 223.79deg 233.52deg,
        #000000 233.52deg 243.25deg,
        #ff0000 243.25deg 252.98deg,
        #000000 252.98deg 262.71deg,
        #ff0000 262.71deg 272.44deg,
        #000000 272.44deg 282.17deg,
        #ff0000 282.17deg 291.9deg,
        #000000 291.9deg 301.63deg,
        #ff0000 301.63deg 311.36deg,
        #000000 311.36deg 321.09deg,
        #ff0000 321.09deg 330.82deg,
        #000000 330.82deg 340.55deg,
        #ff0000 340.55deg 350.28deg,
        #000000 350.28deg 360deg
    );
    transition: transform 3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.roulette__center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.roulette__winning_number {
    color: #fff;
    font-weight: bold;
    font-size: 18px;
}

.roulette__ball {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #fff;
    border-radius: 50%;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    transition: transform 3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.roulette__status {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20px;
}

.roulette__table {
    background: #0f5132;
    border-radius: 15px;
    padding: 20px;
    border: 3px solid #198754;
}

.roulette__numbers_grid {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.roulette__number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #fff;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.roulette__number:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(255,255,255,0.5);
}

.roulette__number.red {
    background: #dc3545;
}

.roulette__number.black {
    background: #000;
}

.roulette__number.zero {
    background: #198754;
    width: 40px;
}

.roulette__numbers_main {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.roulette__row {
    display: flex;
    gap: 2px;
}

.roulette__dozens {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.roulette__dozen {
    flex: 1;
    padding: 15px;
    background: #6f42c1;
    color: #fff;
    text-align: center;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.roulette__dozen:hover {
    background: #5a2d91;
    transform: translateY(-2px);
}

.roulette__quick_bets {
    margin: 20px 0;
}

.roulette__quick_btn {
    display: block;
    width: 100%;
    padding: 10px;
    margin: 5px 0;
    border: none;
    border-radius: 5px;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.roulette__quick_btn.red { background: #dc3545; }
.roulette__quick_btn.black { background: #000; }
.roulette__quick_btn.even, .roulette__quick_btn.odd { background: #6f42c1; }
.roulette__quick_btn.low, .roulette__quick_btn.high { background: #fd7e14; }

.roulette__quick_btn:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.roulette__user_bets {
    margin-top: 20px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 8px;
}

.roulette__user_bets h4 {
    color: #fff;
    margin-bottom: 10px;
}

.roulette__bet_item {
    background: rgba(255,255,255,0.1);
    padding: 8px;
    margin: 5px 0;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
}

.roulette__total_bet {
    color: #ffd700;
    font-weight: bold;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255,255,255,0.3);
}

.roulette__history {
    margin-top: 20px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 8px;
}

.roulette__history h4 {
    color: #fff;
    margin-bottom: 10px;
}

.roulette__history_numbers {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.roulette__history_number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #fff;
    font-weight: bold;
    font-size: 12px;
}

.roulette__history_number.red { background: #dc3545; }
.roulette__history_number.black { background: #000; }
.roulette__history_number.green { background: #198754; }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\BrixCasinoScript\resources\views/roulette.blade.php ENDPATH**/ ?>