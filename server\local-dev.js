const express = require('express');
const http = require('http');
const socketIo = require('socket.io');

const app = express();
const server = http.createServer(app);

// Create Socket.IO server with CORS for local development
const io = socketIo(server, {
    cors: {
        origin: "http://localhost:8000",
        methods: ["GET", "POST"],
        allowedHeaders: ["X-Requested-With", "Content-Type"],
        credentials: true
    },
    serveClient: true,
    allowEIO3: true,
});

let online = 0;
let ipsConnected = [];

// Handle socket connections
io.sockets.on('connection', function(socket) {
    console.log('User connected:', socket.id);
    
    var address = socket.handshake.address;
    if(!ipsConnected.hasOwnProperty(address)) {
        ipsConnected[address] = 1;
        online = online + 1;
    }
    updateOnline(online);
    
    socket.on('disconnect', function() {
        console.log('User disconnected:', socket.id);
        if(ipsConnected.hasOwnProperty(address)) {
            delete ipsConnected[address];
            online = online - 1;
        }
        updateOnline(online);
    });
});

function updateOnline(online) {
    io.sockets.emit('live', {'count': online});
}

// Mock wheel game timer
let wheelTimer = 15;
let wheelActive = false;

function startWheelTimer() {
    if (wheelActive) return;
    
    wheelActive = true;
    wheelTimer = 15;
    
    const timer = setInterval(() => {
        if (wheelTimer > 0) {
            io.sockets.emit('wheel_start', wheelTimer);
            wheelTimer--;
        } else {
            clearInterval(timer);
            wheelActive = false;
            
            // Simulate wheel spin result
            const colors = ['red', 'black', 'green'];
            const result = colors[Math.floor(Math.random() * colors.length)];
            
            io.sockets.emit('wheel_roll', {
                timer: { data: 0 },
                roll: { data: Math.floor(Math.random() * 360) }
            });
            
            setTimeout(() => {
                io.sockets.emit('wheel_clear', {
                    clear: { data: 'clear_all' },
                    last: { data: result },
                    game: { id: Date.now() }
                });
                
                // Start next round
                setTimeout(startWheelTimer, 3000);
            }, 5000);
        }
    }, 1000);
}

// Mock crash game
let crashMultiplier = 1.00;
let crashActive = false;

function startCrashGame() {
    if (crashActive) return;
    
    crashActive = true;
    crashMultiplier = 1.00;
    
    const maxMultiplier = 1 + Math.random() * 10; // Random crash point between 1x and 11x
    
    const timer = setInterval(() => {
        if (crashMultiplier < maxMultiplier) {
            crashMultiplier += 0.01;
            io.sockets.emit('crash', {
                type: 'update',
                multiplier: crashMultiplier.toFixed(2),
                status: 'playing'
            });
        } else {
            clearInterval(timer);
            crashActive = false;
            
            io.sockets.emit('crash', {
                type: 'crashed',
                multiplier: crashMultiplier.toFixed(2),
                status: 'crashed'
            });
            
            // Start next round
            setTimeout(startCrashGame, 5000);
        }
    }, 100);
}

// Mock jackpot timer
function startJackpotTimer() {
    let time = 60; // 1 minute timer
    let min = 1;
    let sec = 0;
    
    const timer = setInterval(() => {
        time--;
        sec--;
        
        if (sec < 0) {
            if (min > 0) {
                min--;
                sec = 59;
            } else {
                clearInterval(timer);
                
                // Simulate jackpot end
                io.sockets.emit('jackpot.slider', {
                    winner: 'Guest_' + Math.random().toString(36).substr(2, 9),
                    amount: Math.floor(Math.random() * 10000)
                });
                
                setTimeout(() => {
                    io.sockets.emit('jackpot.newGame', {
                        id: Date.now(),
                        status: 'active'
                    });
                    setTimeout(startJackpotTimer, 3000);
                }, 5000);
                return;
            }
        }
        
        io.sockets.emit('jackpot.timer', {
            min: min,
            sec: sec,
            time: time,
            timer: 60
        });
    }, 1000);
}

// Start the server
const PORT = 8443;
server.listen(PORT, () => {
    console.log(`Local development Socket.IO server running on port ${PORT}`);
    console.log(`WebSocket URL: http://localhost:${PORT}`);
    
    // Start mock games
    setTimeout(startWheelTimer, 2000);
    setTimeout(startCrashGame, 3000);
    setTimeout(startJackpotTimer, 4000);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    server.close(() => {
        console.log('Server closed.');
        process.exit(0);
    });
});
