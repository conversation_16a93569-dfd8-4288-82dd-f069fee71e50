<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Blackjack;
use App\Models\User;
use App\Models\Settings;
use App\Models\Profit;

class BlackjackController extends Controller
{
    /**
     * Start a new blackjack game
     */
    public function start(Request $request)
    {
        $settings = Settings::where('id', 1)->first();
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Authentication required']);
        }
        
        if ($settings && $settings->blackjack_enabled == 1) {
            return response()->json(['type' => 'error', 'msg' => 'Game mode currently unavailable!']);
        }

        $bet = $request->bet;
        $user = Auth::user();

        // Validation
        if ($bet < 1 || !is_numeric($bet)) {
            return response()->json(['type' => 'error', 'msg' => 'Minimum bet amount is $1!']);
        }
        
        if ($bet > $user->balance) {
            return response()->json(['type' => 'error', 'msg' => 'Insufficient funds']);
        }

        // Check for existing active game
        $existingGame = Blackjack::where('user_id', $user->id)
                                 ->where('status', 'playing')
                                 ->first();
        
        if ($existingGame) {
            return response()->json(['type' => 'error', 'msg' => 'У вас уже есть активная игра']);
        }

        DB::beginTransaction();
        try {
            // Deduct bet from user balance
            $user->balance -= $bet;
            $user->save();

            // Generate deck and deal initial cards
            $deck = Blackjack::generateDeck();
            $playerCards = [array_pop($deck), array_pop($deck)];
            $dealerCards = [array_pop($deck), array_pop($deck)];

            $playerTotal = Blackjack::calculateHandTotal($playerCards);
            $dealerTotal = Blackjack::calculateHandTotal([$dealerCards[0]]); // Only show first card

            // Create game record
            $game = Blackjack::create([
                'user_id' => $user->id,
                'bet' => $bet,
                'player_cards' => $playerCards,
                'dealer_cards' => $dealerCards,
                'player_total' => $playerTotal,
                'dealer_total' => $dealerTotal,
                'status' => 'playing',
                'can_double' => true,
                'can_split' => count($playerCards) == 2 && 
                              Blackjack::getCardValue($playerCards[0]) == Blackjack::getCardValue($playerCards[1])
            ]);

            // Check for immediate blackjack
            if ($game->isBlackjack($playerCards)) {
                $dealerBlackjack = $game->isBlackjack($dealerCards);
                
                if ($dealerBlackjack) {
                    // Push
                    $game->status = 'push';
                    $game->win = $bet; // Return bet
                    $user->balance += $bet;
                } else {
                    // Player blackjack wins
                    $game->status = 'blackjack';
                    $game->win = $bet * 2.5; // 3:2 payout
                    $user->balance += $game->win;
                }
                
                $game->dealer_total = Blackjack::calculateHandTotal($dealerCards);
                $game->save();
                $user->save();
            }

            DB::commit();

            return response()->json([
                'type' => 'success',
                'game' => $game,
                'balance' => $user->balance,
                'dealer_visible_card' => $dealerCards[0] // Only show dealer's first card
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['type' => 'error', 'msg' => 'Ошибка при создании игры']);
        }
    }

    /**
     * Hit - draw another card
     */
    public function hit(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }

        $user = Auth::user();
        $game = Blackjack::where('user_id', $user->id)
                         ->where('status', 'playing')
                         ->first();

        if (!$game) {
            return response()->json(['type' => 'error', 'msg' => 'Активная игра не найдена']);
        }

        // Generate new card
        $deck = Blackjack::generateDeck();
        $newCard = array_pop($deck);
        
        $playerCards = $game->player_cards;
        $playerCards[] = $newCard;
        
        $playerTotal = Blackjack::calculateHandTotal($playerCards);
        
        $game->player_cards = $playerCards;
        $game->player_total = $playerTotal;
        $game->can_double = false; // Can't double after hitting
        $game->can_split = false; // Can't split after hitting

        // Check for bust
        if ($playerTotal > 21) {
            $game->status = 'bust';
            $this->recordProfit($game->bet);
        }

        $game->save();

        return response()->json([
            'type' => 'success',
            'game' => $game,
            'new_card' => $newCard
        ]);
    }

    /**
     * Stand - end player turn and play dealer
     */
    public function stand(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }

        $user = Auth::user();
        $game = Blackjack::where('user_id', $user->id)
                         ->where('status', 'playing')
                         ->first();

        if (!$game) {
            return response()->json(['type' => 'error', 'msg' => 'Активная игра не найдена']);
        }

        // Play dealer hand
        $this->playDealerHand($game);
        
        return response()->json([
            'type' => 'success',
            'game' => $game,
            'balance' => $user->balance
        ]);
    }

    /**
     * Play dealer hand according to rules
     */
    private function playDealerHand($game)
    {
        $dealerCards = $game->dealer_cards;
        $deck = Blackjack::generateDeck();
        
        // Dealer hits on soft 17
        while (Blackjack::calculateHandTotal($dealerCards) < 17) {
            $dealerCards[] = array_pop($deck);
        }
        
        $dealerTotal = Blackjack::calculateHandTotal($dealerCards);
        $playerTotal = $game->player_total;
        
        $game->dealer_cards = $dealerCards;
        $game->dealer_total = $dealerTotal;
        
        // Determine winner
        $user = User::find($game->user_id);
        
        if ($dealerTotal > 21) {
            // Dealer bust, player wins
            $game->status = 'player_win';
            $game->win = $game->bet * 2;
            $user->balance += $game->win;
            $this->recordProfit(-$game->bet);
        } elseif ($dealerTotal > $playerTotal) {
            // Dealer wins
            $game->status = 'dealer_win';
            $this->recordProfit($game->bet);
        } elseif ($playerTotal > $dealerTotal) {
            // Player wins
            $game->status = 'player_win';
            $game->win = $game->bet * 2;
            $user->balance += $game->win;
            $this->recordProfit(-$game->bet);
        } else {
            // Push
            $game->status = 'push';
            $game->win = $game->bet;
            $user->balance += $game->bet;
        }
        
        $game->save();
        $user->save();
    }

    /**
     * Record profit for the house
     */
    private function recordProfit($amount)
    {
        Profit::create([
            'game' => 'blackjack',
            'sum' => $amount
        ]);
    }

    /**
     * Double down - double the bet and take exactly one more card
     */
    public function double(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }

        $user = Auth::user();
        $game = Blackjack::where('user_id', $user->id)
                         ->where('status', 'playing')
                         ->first();

        if (!$game) {
            return response()->json(['type' => 'error', 'msg' => 'Активная игра не найдена']);
        }

        if (!$game->can_double) {
            return response()->json(['type' => 'error', 'msg' => 'Удвоение недоступно']);
        }

        if ($user->balance < $game->bet) {
            return response()->json(['type' => 'error', 'msg' => 'Недостаточно средств для удвоения']);
        }

        DB::beginTransaction();
        try {
            // Deduct additional bet
            $user->balance -= $game->bet;
            $user->save();

            // Double the bet
            $game->bet *= 2;

            // Draw one card
            $deck = Blackjack::generateDeck();
            $newCard = array_pop($deck);

            $playerCards = $game->player_cards;
            $playerCards[] = $newCard;

            $playerTotal = Blackjack::calculateHandTotal($playerCards);

            $game->player_cards = $playerCards;
            $game->player_total = $playerTotal;
            $game->can_double = false;
            $game->can_split = false;

            // Check for bust
            if ($playerTotal > 21) {
                $game->status = 'bust';
                $this->recordProfit($game->bet);
            } else {
                // Automatically stand after doubling
                $this->playDealerHand($game);
            }

            $game->save();
            DB::commit();

            return response()->json([
                'type' => 'success',
                'game' => $game,
                'new_card' => $newCard,
                'balance' => $user->balance
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['type' => 'error', 'msg' => 'Ошибка при удвоении']);
        }
    }

    /**
     * Split pairs (placeholder - not fully implemented)
     */
    public function split(Request $request)
    {
        return response()->json(['type' => 'error', 'msg' => 'Разделение пар пока не реализовано']);
    }

    /**
     * Get current game status
     */
    public function status(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['type' => 'error', 'msg' => 'Необходимо авторизоваться']);
        }

        $user = Auth::user();
        $game = Blackjack::where('user_id', $user->id)
                         ->where('status', 'playing')
                         ->first();

        if (!$game) {
            return response()->json(['type' => 'error', 'msg' => 'Активная игра не найдена']);
        }

        return response()->json([
            'type' => 'success',
            'game' => $game,
            'dealer_visible_card' => $game->dealer_cards[0]
        ]);
    }
}
