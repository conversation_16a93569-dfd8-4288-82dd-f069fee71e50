<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $table = 'users';

    protected $fillable = [
        'id','username', 'password', 'avatar', 'ip', 'balance', 'wager', 'vk_id', 'tg_id', 'unique_id', 'admin', 'youtuber','ref_code', 'referred_by', 'ban', 'bonus_time', 'api_token'
    ];
}
