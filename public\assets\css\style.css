@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
* {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style-type: none;
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
}

:root {
    --color-main-bg: #16171d;
    --color-inner: #1B1C24;
    --color-nav-active: #D9D9D907;
    --main-color: #2AC800;
    --btn-color-bg: linear-gradient(177.2deg, #2AC800 2.33%, rgba(0, 194, 77, 0) 212.11%);
    --x2: #535353;
    --x3: #dbd820;
    --x5: #F5453B;
    --x50: #01C236;
}

body {
    height: 100vh;
    background: var(--color-main-bg);
}

button:disabled {
    opacity: 0.7;
}

[class*="__container"] {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: 1280px;
    border-radius: 25px;
}

.main__error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    height: 100vh;
}

.main__error .code__error{
    color: #FFFFFF;
    font-weight: 700;
    font-size: 32px;
}

.main__error .error__text {
    color: #FFFFFF;
    font-weight: 600;
    font-size: 21px;
}

.back__link {
    margin: 25px auto;
    background: linear-gradient(177.2deg, #2AC800 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 8px;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    padding: 11px 15px;
    color: #FFFFFF;
    cursor: pointer;
    width: 200px;
}

.header {
    display: flex;
    width: 100%;
    height: 70px;
    background: var(--color-inner);
}

.navbar {
    position: fixed;
    width: 100%;
    z-index: 3;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 20px;
    background: var(--color-inner);
    height: 70px;
}

.logo {
    text-align: center;
    padding: 0;
    margin-left: 0;
    display: flex;
    align-items: center;
    z-index: 10;
    font-family: 'Inter', sans-serif;
    font-style: normal;
    font-weight: 900;
    font-size: 32px;
    line-height: 42px;
    color: #FFFFFF;
}

.green-x {
    color: #2AC800;
    text-shadow: 0 0 7px #2AC800,
    0 0 25px #2AC800;
}

.bonus_green {
    color: #2AC800;
}

.navmenu {
    flex-shrink: 0;
    margin-left: 50px;
}

.navmenu__list {
    display: flex;
    gap: 5px;
}

.navmenu__item_link {
    position: relative;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 5px;
}

.navmenu__item_link:hover {
    color:var(--main-color)
}

.navmenu__item_link.active:after {
    color: #fff;
    position: absolute;
    bottom: -18px;
    display: block;
    width: 100%;
    height: 3px;
    -ms-touch-action: none;
    touch-action: none;
    cursor: default;
    content: "";
    background-color: var(--main-color);
    -webkit-transition: background-color .3s;
    transition: background-color .3s;
    border-radius: 50px;
}

.auth__btn {
    margin-left: auto;
    background: linear-gradient(177.2deg, #2AC800 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 8px;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    padding: 11px 15px;
    color: #FFFFFF;
    cursor: pointer;
}

.user__wallet {
    margin-left: auto;
    display: flex;
    gap: 10px;
    align-items: center;
}

.user__balance {
    color: #FFFFFF;
    font-weight: 600;
    font-size: 15px;
    border-radius: 8px;
    padding: 11px 15px;
    border-bottom: 3px solid var(--main-color);
    background-color: var(--color-main-bg);
}

.user__balance i {
    color: var(--main-color)
}

.wallet_up_btn {
    background: var(--btn-color-bg);
    border-radius: 8px;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    padding: 11px 15px;
    color: #FFFFFF;
    cursor: pointer;
}

.auth__btn a {
    color: #FFFFFF;
}

.fix__left_nav {
    width: 60px;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    bottom: 0;
    background: var(--color-inner);
    display: flex;
    flex-direction: column;
}

.leftside__games {
    margin-top: 70px;
}

.leftside__game {
    margin-top: 3px;
}

.leftside__game a {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    margin: auto;
    padding: 6px;
    font-size: 24px;
    color: #a9a9a9;
    box-sizing: content-box;
    background-color: transparent;
    border-radius: 10px;
    -webkit-transition: color .3s,background-color .3s;
    transition: color .3s,background-color .3s;
    margin-top: 2px;
}

.leftside__game a:hover {
    color: var(--main-color);
    background-color: var(--color-main-bg);
}

.leftside__social {
    margin-top: auto;
}

.leftside__social {
    margin-top: auto;
    padding-top: 5px;
    border-top: 1px solid #383838;
}

.social a {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: transparent;
    -webkit-transition: color .3s,background-color .3s;
    transition: color .3s,background-color .3s;
    font-size: 24px;
    color: #7E7E7E;
    margin-bottom: 10px;
}

.social a:last-child {
    margin-bottom: 15px;
}

.social_tg a:hover {
    color: #36B8FF;
}

.social_vk a:hover {
    color: #0077FF;
}

.main__content {
    display: flex;
    flex-direction: column;
    top: 90px;
    position: relative;
    padding-right: 15px;
    padding-left: 75px;
    min-height: calc(100vh - 100px);
}

.games__container {
    position: relative;
    margin: 0 auto;
    max-width: 1280px;
    width: 100%;
    background-color: var(--color-inner);
    border-radius: 25px;
}

.games__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill,minmax(250px,1fr));
    grid-gap: 20px;
    padding: 25px;
}

.bonus__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill,minmax(400px,2fr));
    grid-gap: 30px;
    padding: 35px 50px;
}

.bonus__container {
    background-color: var(--color-inner);
}

.games__item {
    display: flex;
    transition: .3s all ease;
    will-change: transform;
}

.games__card {
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: 20px;
}

.card_mines {
    background-image: url(/assets/images/games/mines.png);
}

.card_wheel {
    background-image: url(/assets/images/games/wheel.png);
}

.card_dice{
    background-image: url(/assets/images/games/dice.png);
}

.card_jackpot{
    background-image: url(/assets/images/games/jackpot.png);
}

.card_crash{
    background-image: url(/assets/images/games/crash.png);
}

.card_other {
    background-image: url(/assets/images/games/other.png);
}

.card_btn {
    position: absolute;
    bottom: 10%;
    left: 50%;
    margin: 0 auto;
    text-align: center;
    transform: translate(-50%, 0%);
}

.card__play {
    text-align: center;
    display: flex;
    padding: 15px 35px;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    border-radius: 10px;
    font-size: 1rem;
    color: #fff;
    font-weight: 500;
    background: rgba(255,255,255,.15);
    transition: .25s all ease;
}

.card__play:hover {
    background-color: var(--main-color);
}

.footer {
    margin: auto auto 5px;
    max-width: 1280px;
    display: flex;
    flex-direction: column;
    color: #7E7E7E;
    padding-top: 60px;
    width: 100%;
}

.footer__header {
    display: flex;
    width: 100%;
    padding-bottom: 15px;
    border-bottom: 1px solid #383838;
}

.footer__warn {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: auto;
}

.warn_mark {
    display: inline-flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    font-weight: 800;
    color: #fff;
    text-align: center;
    background: #B5111E;
    border-radius: 50%;
}

.warn_text {
    font-weight: 400;
    font-size: 12.6px;
    line-height: 15px;
    display: flex;
    align-items: center;
    color: #7E7E7E;
    max-width: 350px;
}

.footer__bottom {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    font-weight: 500;
    font-size: 12.6px;
    line-height: 15px;
    display: flex;
    align-items: center;
}

.footer__bottom a {
    color: #7E7E7E;
}

.footer_security {
    font-weight: 500;
    font-size: 12px;
}

.bonus__daily {
    display: flex;
    padding: 25px 40px;
    justify-content: space-between;
    background-color: var(--color-main-bg);
    align-items: center;
    border-radius: 15px;
}

.daily_btn {
    padding: 10px 15px;
    background: var(--btn-color-bg);
    border-radius: 10px;
    border-color: transparent;
    color: #FFFFFF;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    cursor: pointer;
}

.bonus__text {
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    align-items: center;
    color: #FFFFFF;
    max-width: 225px;
}

.bonus__vk {
    display: flex;
    padding: 25px 40px;
    justify-content: space-between;
    background: #0077FF;
    backdrop-filter: blur(250px);
    align-items: center;
    border-radius: 15px;
    background-image: url(/assets/images/bonus_vk.png);
    background-repeat: no-repeat;
}

.bonus__tg {
    display: flex;
    padding: 25px 40px;
    justify-content: space-between;
    background: #36B8FF;
    backdrop-filter: blur(250px);
    align-items: center;
    border-radius: 15px;
    background-image: url(/assets/images/bonus_tg.png);
    background-repeat: no-repeat;
}

.vk_bonus, .tg_bonus {
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #FFFFFF;
    background-color: transparent;
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all .3s;
}

.vk_bonus, .tg_bonus:hover {
    background-color: #FFFFFF;
    color: #000000;
}

.vk_bonus.success, .tg_bonus.success {
    background-color: #FFFFFF;
    color: #000000;
}

.bonus__promo {
    display: flex;
    flex-direction: column;
    padding: 25px 40px;
    background-color: var(--color-main-bg);
    border-radius: 15px;
}

.bonus__inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px
}

.input__wrapper {
    height: 3rem;
    padding: 0 0.9rem 0 1.1rem;
    overflow: hidden;
    color: #716f6f;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    border: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%) solid 1px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    transition: border-color .3s;
    cursor: pointer;
    width: 275px;
}

.promo__input {
    color: #7E7E7E;
    width: 100%;
    height: 100%;
    background: none;
    border: none;
    outline: none;
}

.promo__activate_btn {
    padding: 10px 15px;
    background: var(--btn-color-bg);
    border-radius: 10px;
    border-color: transparent;
    color: #FFFFFF;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    cursor: pointer;
}

.mobile_menu {
    display: none;
    height: 55px;
    position: fixed;
    bottom: 0;
    width: 100%;
    background: var(--color-inner);
    z-index: 100;
}

.mobile_menu__content {
    position: relative;
    height: 100%;
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 5px;
    margin: auto;
    font-size: 11px;
    color: #7E7E7E;
}

.mobile_menu__link {
    display: block;
    width: 100%;
    text-align: center;
    padding: 5px 0;
    color: #7E7E7E;
}

.mobile_menu__link i {
    display: block;
    margin-bottom: 5px;
    font-size: 22px;
}

.modal__window {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    overflow: hidden;
    outline: 0;
    visibility: hidden;
    opacity: 0;
    transition: .2s all ease;
    overflow-x: hidden;
    overflow-y: hidden;
}

.modal__window.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0,0,0, 0.5);
}

.modal__dialog.modal__auth {
    width: 100%;
    max-width: 420px;
    justify-content: center;
}

.modal__dialog.modal__bind_tg {
    width: 100%;
    max-width: 550px;
    justify-content: center;
}

.modal__dialog {
    width: 100%;
    position: relative;
    z-index: 11;
    flex: 0 1 auto;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    align-items: center;
    min-height: 100%;
    padding: 10px 5px;
}

.modal-dialog.modal-auth .modal {
    max-width: 420px;
    width: 100%;
}

.modal {
    border-radius: 30px;
    position: relative;
    background: var(--color-main-bg);
    overflow: hidden;
}

.modal__heading {
    padding: 20px;
    background: rgb(34, 37, 47);
    font-weight: 400;
    margin-bottom: 0;
    font-size: 18px;
    line-height: 20px;
    color: #FFFFFF;
}

.modal__content {
    padding: 20px 15px;
}

.auth__modal {
    display: flex;
}

.auth__modal_content {
    flex: 1;
} 

.auth__modal_content {
    padding: 20px;
    flex-direction: column;
    display: flex;
    height: 100%;
}

.auth__modal_head {
    font-weight: 500;
    font-size: 32px;
    line-height: 39px;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
    padding-bottom: 10px;
    border-bottom: 1px solid #1E1E1E;
}

.auth__social_btn_unactive {
    pointer-events: none;
    opacity: .6;
}

.auth__checkbox {
    font-size: .9rem;
    color: #7E7E7E;
    margin-top: 20px;
    margin-bottom: 30px;
}

.modal__close {
    color: #fff;
    top: 17px;
    right: 0;
    padding: 10px;
    font-size: 12px;
    position: absolute;
    right: 12px;
    cursor: pointer;
    transform: rotate(45deg);
    font-size: 24px;
}

.auth__checkbox .checkbox {
    margin-bottom: 15px;
}

.auth__rules span {
    font-weight: 400;
    font-size: 15px;
    line-height: 18px;
    display: flex;
    gap: 3px;
    align-items: center;
    text-align: center;
    color: #7E7E7E;
}

.colored-link {
    color: #2AC800;
}

.custom-checkbox+label {
    display: inline-flex;
    align-items: center;
    user-select: none;
}

.helps__bing_tg {
    color: #a9a9a9;
    font-size: 15px;
    font-weight: 500;
}

.bind__tg_code {
    position: relative;
    margin-top: 15px;
    width: 100%;
}

.bint_code_form {
    justify-content: center;
    max-width: 250px;
    margin: 15px auto;
    align-items: center;
    width: 100%;
    display: flex;
    max-width: 500px;
    margin: auto;
}

.bind_code {
    background: rgb(34, 37, 47);
    border-right: 0;
    border-radius: 8px;
    outline: none;
    border-color: transparent;
    min-width: 0;
    max-width: 250px;
    width: 100%;
    height: 40px;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 500;
    padding-left: 10px;
}

[type$=checkbox] {
    -webkit-appearance: none;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    padding: 9px;
    border-radius: 10px;
    display: inline-block;
    position: absolute;
    width: 40px;
    height: 40px;
}

[type$=checkbox]:checked:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url(/assets/images/tick.png);
    background-position: center center;
    background-repeat: no-repeat;
    border-radius: 3px;
}

.custom-checkbox+label:before {
    content: '';
    display: inline-block;
    width: 3em;
    height: 5.5em;
    flex-shrink: 0;
    flex-grow: 0;
    border-radius: 0.25em;
    margin-right: 0.5em;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%;
}

.auth__social {
    display: flex;
    gap: 20px;
    padding-bottom: 35px;
    border-bottom: 1px solid #1E1E1E;
}

.auth__social_btn {
    background: #1B1C24;
    color: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    cursor: pointer;
    margin-right: 7px;
    position: relative;
    flex: 1;
    max-width: 115px;
}

.auth__social_google {
    background-image: url(/assets/images/auth-social/google.png);
    background-repeat: no-repeat;
    background-position: center center;
}

.auth__social_vk {
    background-image: url(/assets/images/auth-social/vk.png);
    background-repeat: no-repeat;
    background-position: center center;
}

.auth__social_steam{
    background-image: url(/assets/images/auth-social/steam.png);
    background-repeat: no-repeat;
    background-position: center center;
}

.notification {
    right: 0;
    bottom: 0;
    position: fixed;
}

.notification-wrapper {
    display: block;
    overflow: hidden;
    width: 100%;
    margin: 0;
    padding: 0;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 15px;
    border-radius: 6px;
}

.notification-wrapper.error {
    background-color: rgba(216,51,51,.7);
    text-align: center;
    margin-bottom: 5px;
}

.notification-wrapper.success {
    background-color: rgba(22, 145, 81, .7);
    text-align: center;
    margin-bottom: 5px;
}

.notification-wrapper.error i {
    font-size: 22px;
    vertical-align: middle;
}


.notification-wrapper.success i {
    font-size: 22px;
    vertical-align: middle;
}

.bonus__rules {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
    padding-bottom: 35px;
}

.rules__sub {
    font-size: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 3px solid var(--main-color);
    color: #FFFFFF;
    border-radius: 10px;
    transition: all ease .3s;
}

.rules__sub:hover {
    background-color: var(--main-color);
}

.games__area {
    display: flex;
    gap: 20px;
    align-items: stretch;
    min-width: 0;
}

.games__sidebar {
    min-height: 475px;
    min-width: 325px;
    max-width: 325px;
    background-color: var(--color-inner);
    border-radius: 30px;
}

.games__sidebar_label {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 15px;
    display: flex;
    align-items: center;
    color: #CDCDCD;
    margin-bottom: 10px;
}

.games__sidebar {
    padding: 30px;
}

.games__input_wrapper_bet {
    margin-bottom: 20px;
}

.games__input_wrapper_bombs {
    margin-bottom: 35px;
}

.games__sidebar_input {
    width: 100%;
    background: #20242D;
    border: 1px solid #3B3C3E;
    border-radius: 8px;
    outline: none;
    padding: 0 10px;
    color: #FFFFFF;
    font-size: 18px;
    font-weight: 500;
    height: 45px;
    box-sizing: border-box;
}

.games__sidebar_help_bombs {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.games__sidebar_bombs_action:first-of-type {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.games__sidebar_bombs_action {
    background: rgba(34, 37, 47, 0.8);
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    color: rgba(205, 205, 205, 0.8);
    flex: auto;
    outline: none;
    border: none;
    border-right: 1px solid var(--color-inner);
    line-height: 30px;
    cursor: pointer;
}

.games__sidebar_bombs_action:last-of-type {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    border-right: none;
}

.sidebar__play {
    width: 100%;
    background: linear-gradient(177.2deg, #2AC800 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    font-weight: 500;
    font-size: 19px;
    line-height: 18px;
    color: #FFFFFF;
    padding: 20px 15px;
    border-color: transparent;
    cursor: pointer;
}

.sidebar__take_win{
    width: 100%;
    background: linear-gradient(177.2deg, #2AC800 2.33%, rgba(0, 194, 77, 0) 212.11%);
    border-radius: 10px;
    font-weight: 500;
    font-size: 19px;
    line-height: 18px;
    color: #FFFFFF;
    padding: 20px 15px;
    border-color: transparent;
    cursor: pointer;
}

.games__field {
    width: 100%;
    background: #1B1C24;
    border-radius: 30px;
    min-height: 500px;
}

.dice__field {
    width: 100%;
    background: #1B1C24;
    border-radius: 30px;
}

.game__field_mines {
    display: flex;
    flex-direction: column;
    height: 100%;
    align-items: stretch;
    position: relative;
}

.game__field_crash {
    display: flex;
    flex-direction: column;
    height: 100%;
    --x2: #951c99;
    width: 100%;
    max-width: 935px;
}

.game__mines_coefs {
    display: flex;
    max-width: 100%;
    padding: 20px 25px;
    background: #1E1F27;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    max-width: 935px;
    overflow-x: hidden;
    overflow-y: hidden;
}

.game__wheel_coefs {
    display: flex;
    max-width: 100%;
    padding: 20px 25px;
    background: #1E1F27;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
}

.game__crash_coefs {
    display: flex;
    min-width: 100%;
    padding: 20px 25px;
    background: #1E1F27;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    gap: 4px;
}

.referrals__container {
    display: flex;
    justify-content: center;
}

.mines_coef.active {
    border: 3px solid var(--main-color);
    color: #FFFFFF;
}

.game__mines_coef {
    display: flex;
    gap: 5px;
}

.game__wheel_coef {
    display: flex;
    gap: 4px;
}

.game__crash_coef {
    display: flex;
    gap: 4px;
}

.crash__coef {
    background: transparent;
    border: 3px solid #2AC800;
    padding: 4px 10px;
    color: #2AC800;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
}

.history__line--yellow {
    height: 21px;
    background: var(--x3);
}

.history__line--black {
    height: 18px;
    background: var(--x2);
}

.history__line--red {
    height: 25px;
    background: var(--x5);
}

.history__line--green {
    height: 28px;
    background: var(--x50);
}

.history__line {
    border-radius: 15px;
    width: 4px;
}

.mines_coef {
    border: 3px solid #474747;
    border-radius: .5em;
    padding: 10px 25px;
    display: inline-block;
    text-align: center;
    color: #BCBCBC;
    margin-right: 5px;
}

.games__area_field {
    flex: auto;
    padding: 25px 70px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mines__field {
    width: 400px;
    display: flex;
    flex-wrap: wrap;
    margin: auto;
}

.mines__cell {
    display: flex;
    background: var(--main-color);
    border-radius: 5px;
    height: 70px;
	width: 70px;
    margin: 4px;
    cursor: pointer;
    border-color: transparent;
}

.mines__cell.active {
    background-color: var(--color-nav-active);
    border: 3px solid var(--main-color);
}

.mines__cell .cristal {
    margin: auto;
    height: 65px;
	width: 65px;
}

.bomb {
    margin: auto;
    width: 48px;
    height: 48px;
}

.games__sidebar_play_button {
    position: relative;
}

.game__dice_wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.dice__possible_win {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 105px;
    line-height: 127px;
    color: #2AC800;
}

.dice__possible_text {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 19px;
    color: #757575;
    margin-bottom: 25px;
}

.dice__play_buttons {
    display: flex;
    gap: 15px;
}

.dice__play {
    padding: 15px 65px;
    background: var(--btn-color-bg);
    border-radius: 10px;
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #FFFFFF;
    border-color: transparent;
    cursor: pointer;
}

.dice__main_area {
    text-align: center;
}

.dice__result {
    width: 100%;
    margin-top: 15px;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 45px;
    color: #737373;
    border: 2px solid rgba(59, 60, 62, 0.8);
    border-radius: 8px;
}

.max__prog, .min__prog {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    color: #757575;
    margin-top: 10px;
}

.dice__action {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.dice__result.danger {
    border-color: rgb(216,51,51);
    color: rgb(216,51,51);
} 

.dice__result.success {
    border-color: var(--main-color);
    color: var(--main-color);
}

.modal__wallet {
    background: var(--color-inner);
    border-radius: 30px;
    max-width: 100%;
    padding: 30px;
    max-width: 1280px;
    min-height: 500px;
    margin: 0 auto;
}

.modal__wallet_header {
    display: flex;
    position: relative;
}

.modal__wallet_header_nav {
    display: flex;
    position: relative;
    background: rgb(34, 37, 47);
    border-radius: 10px;
    flex-wrap: wrap;
    padding: 0 15px;
    width: 100%;
    align-items: center;
    height: 50px;
}

.modal__wallet_header_item {
    align-items: center;
    background: transparent;
    color: #FFFFFF;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    height: 45px;
    position: relative;
    border-color: transparent;
    margin-left: 20px;
    gap: 5px;
    align-items: center;
    cursor: pointer;
}

.modal__wallet_header_item i {
    font-size: 24px;
}

.modal__wallet_header_item:first-child {
    margin-left: 0;
}

.modal__wallet_header_item.active {
    color: var(--main-color);
}

.modal__wallet_close {
    margin-left: auto;
    font-size: 21px;
    border-color: transparent;
    border-radius: 100px;
    background-color: transparent;
    color: #FFFFFF;
    cursor: pointer;
    transition: all ease .3s;
}

.modal__wallet_close i {
    transform: rotate(45deg);
}

.modal__wallet_close:hover {
    color: var(--main-color);
}

.modal__wallet_body {
    display: flex;
}

.modal__wallet_body_paysys {
    padding-right: 25px;
    padding-top: 15px;
    width: 27.89%;
}

.modal__wallet_body-label {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    color: #FFFFFF;
}

.modal__wallet_balance {
    margin-top: 10px;
    align-items: center;
    background: rgb(34, 37, 47);
    border-radius: 5px;
    box-shadow: 0 5px 15px rgb(34, 37, 47);
    color: #FFFFFF;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    height: 45px;
    line-height: 17px;
    margin-bottom: 10px;
    padding: 16px 19px;
    width: 100%;
    box-sizing: border-box;
}

.modal__wallet_pay_method, .modal__wallet_withdraw_method {
    align-items: center;
    background: rgb(34, 37, 47);
    border: 2px solid rgb(34, 37, 47);
    border-radius: 5px;
    color: #fff;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    height: 45px;
    line-height: 17px;
    padding: 16px 19px;
    position: relative;
    width: 100%;
    margin-top: 8px;
    cursor: pointer;
}

.modal__wallet_pay_method img {
    height: 24px;
    margin-right: 10px;
    width: 24px;
}

.modal__wallet_withdraw_method img {
    height: 24px;
    margin-right: 10px;
    width: 24px;
}

.modal__wallet_pay_method.active, .modal__wallet_withdraw_method.active {
    border: 2px solid var(--main-color);
}

.modal__wallet_body_pay {
    display: flex;
    flex-direction: column;
    padding-left: 25px;
    padding-top: 15px;
    width: 72.11%;
}

.modal__wallet_input_wrapper {
    position: relative;
}

.modal__wallet_input_currency {
    font-size: 16px;
    font-weight: 500;
    align-items: center;
    display: flex;
    justify-content: center;
    left: 14px;
    position: absolute;
    top: 60%;
    transform: translateY(-50%);
    color: #FFFFFF;
}

.modal__wallet_input {
    padding-left: 38px;
    margin-top: 10px;
    background: rgb(34, 37, 47);
    border-radius: 5px;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 500;
    height: 45px;
    width: 100%;
    padding: 16px 19px 16px 38px;
    box-sizing: border-box;
    border-color: transparent;
    outline: none;
}

.withdraw__img_method {
    position: absolute;
    width: 28px;
    height: 28px;
    margin-left: 15px;
}

.modal__wallet_sumbit {
    border-color: transparent;
    background: var(--btn-color-bg);
    border-radius: 5px;
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    text-align: center;
    color: #FFFFFF;
    padding: 12px 0px;
    width: 100%;
    margin-top: 20px;
    cursor: pointer;
}

.modal__wallet_rates {
    border-top: 1px solid rgb(34, 37, 47);
    color: #FFFFFF;
    font-size: 12px;
    font-weight: 500;
    line-height: 15px;
    margin-top: 15px;
    padding-top: 15px;
    text-align: center;
}

.history__content {
    width: 100%;
}

.history__types {
    width: 100%;
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 15px;
}

.history__type {
    border-radius: 10px;
    background: rgb(34, 37, 47);
    padding: 10px 20px;
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    border-color: transparent;
    cursor: pointer;
}

.history__type.active {
    background: linear-gradient(184.33deg, #0077FF 3.52%, rgba(0, 0, 0, 0) 403.93%);
}

.history__table {
    width: 100%;
    --table-cols: 15% 20% 20% 25% 20%;
    border-collapse: collapse;
    margin-top: 20px;
}

.history__table>thead {
    display: grid;
    height: 56px;
    align-content: center;
    border-top-right-radius: 15px;
}

.history__table>thead>tr {
    display: grid;
    grid-template-columns: var(--table-cols);
    justify-content: start;
    justify-items: start;
    color: #FFFFFF;
    padding: 0 20px;
}

.history__table>thead>tr>td {
    width: 100%;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 29px;
    color: #FFFFFF;
}

.tb-center {
    text-align: center;
}

.history__table tbody {
    overflow: hidden;
    position: relative;
    display: block;
}

.history__table>tbody>tr {
    height: 65px;
}

.history__table>tbody>tr {
    display: grid;
    grid-template-columns: var(--table-cols);
    height: 65px;
    align-content: center;
    align-items: center;
    animation-fill-mode: forwards;
    animation-duration: .5s;
    animation-timing-function: ease-out;
    will-change: transform,opacity;
    padding: 0 20px;
    background: rgb(34, 37, 47);
    margin-bottom: 15px;
    border-radius: 10px;
}

.history__table tbody>tr>td {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    color: #FFFFFF;
}

.method__img {
    width: 36px;
    height: 36px;
    margin-bottom: -5px;
}

.stats__controls {
    margin-top: 10px;
    left: 0;
    width: 100%;
    display: none;
}

.stats__controls.active {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
}

.btn__full-stats {
    padding: 8px;
    outline: none;
    border: solid 1px #424242;
    background: 0 0;
    color: #a9a9a9;
    border-radius: 5px;
    opacity: .4;
    font-size: 14px;
    font-weight: 600;
	font-family: 'Rubik', sans-serif;
}

.btn__full-stats.active {
    opacity: 1;
    cursor:pointer;
}

.btn__full-stats.active:hover {
    color: #c4c4c4;
}

.btn-next {
    margin-left: auto;
}

.referrals__title {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

.refferals__info {
    display: flex;
    gap: 25px;
    margin-bottom: 25px;
}

.block__refferals_info {
    background: var(--color-inner);
    border-radius: 15px;
    width: 100%;
    padding: 20px;
}

.referrals__system {
    position: relative;
    background: var(--color-inner);
    border-radius: 15px;
    padding: 30px;
    min-width: 745px;
    max-width: 745px;
    background-image: url(/assets/images/referral/coin1.png);
    background-repeat: no-repeat;
    background-position-x: 100%;
}

.amount__refferals {
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #FFFFFF;
    margin-bottom: 5px;
}

.amount__refferals_title {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    color: #FFFFFF;
}

.referrals_system_title {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.referrals_system_description {
    max-width: 300px;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

.referrals__link {
    display: flex;
    gap: 15px;
}

.copy__referrals_link {
    background: var(--btn-color-bg);
    color: #FFFFFF;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 19px;
    text-align: center;
    outline: none;
    border-radius: 10px;
    border-color: transparent;
    height: 45px;
    cursor: pointer;
    padding: 0 10px;
}

.referrals__link_input {
    color: #FFFFFF;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    padding-left: 10px;
    width: 100%;
    max-width: 325px;
    height: 45px;
    border-radius: 10px;
    border-color: transparent;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.188) -164.91%, rgba(217, 217, 217, 0.032) -60.22%);
    outline: none;
}

.wheel__colors {
    width: 100%;
    display: flex;
    gap: 5px;
}

.wheel__bet_color {
    width: 100%;
    padding: 10px 20px;
    border-radius: 5px;
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    align-items: center;
    text-align: center;
}

.wheel__x2 {
    background-color: var(--x2);
}

.wheel__x3 {
    background-color: var(--x3);
}

.wheel__x5 {
    background-color: var(--x5);
}

.wheel__x50 {
    background-color: var(--x50);
}

.wheel__wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 70px 20px 55px;
}

.wheel__wrapper svg {
    width: 400px;
    height: 400px;
    position: absolute;
}

.game-roller__x2 {
    fill: #535353;
}

.game-roller__x3 {
    fill: #CCC3FF;
}

.game-roller__x5 {
    fill: #F5453B;
}

.game-roller__x50 {
    fill: #01C236;
}

.game-roller{
    position: absolute;
    width: 380px;
    height: 380px;
    will-change: transform;
    -webkit-transition-timing-function: cubic-bezier(.51,.18,.22,1);
    transition-timing-function: cubic-bezier(.51,.18,.22,1);
}

.wheel__info {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 320px;
    height: 320px;
    padding: 20px;
}

.game__wheel {
    width: 350px;
    height: 350px;
    position: relative;
    margin: auto;
}

.wheel__timer {
    font-family: open sans,sans-serif;
    color: #fff;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    color: #daf6ff;
    text-shadow: 0 0 25px #2AC800, 0 0 25px transparent;
    letter-spacing: .05em;
    font-size: 34px;
    font-weight: 800;
    padding: 5px 0;
}

.wheel__pointer {
    color: transparent;
    position: absolute;
    left: 50%;
    top: 15px;
    z-index: 2;
    border-color: transparent transparent #2AC800 transparent;
    border-style: solid;
    border-width: 0 7px 15px 7px;
    -webkit-transition: border-color .3s;
    transition: border-color .3s;
}

.wheel__timer .time {
    min-width: 2em;
    font-size: 2.5rem;
    font-weight: 500;
    color: #FFFFFF
}

.wheel__bet_history {
    margin-top: 10px;
    background: var(--color-inner);
    padding: 15px;
    border-radius: 12px;
}

.wheel__colors_history {
    display: flex;
}

.wheel__history_color {
    flex: 1;
    min-width: 0;
    margin-right: 10px;
    cursor: pointer;
}

.wheel__color_x2 .wheel__history_col_head {
    display: flex;
    justify-content: space-between;
    background: var(--x2);
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
}

.wheel__color_x3 .wheel__history_col_head {
    display: flex;
    justify-content: space-between;
    background: var(--x3);
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
}

.wheel__color_x5 .wheel__history_col_head {
    display: flex;
    justify-content: space-between;
    background: var(--x5);
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
}

.wheel__color_x50 .wheel__history_col_head {
    display: flex;
    justify-content: space-between;
    background: var(--x50);
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
}

.wheel__history_col_head {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    color: #fff;
    padding: 15px;
}

.profile__wrapper {
    background-color: var(--color-inner);
    padding: 35px 50px;
    border-radius: 15px;
}

.profile__header {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #383838;
}

.profile__name {
    color: #FFFFFF;
    font-weight: 500;
    font-size: 24px;
}

.profile__logout {
    color: #FFFFFF;
    font-weight: 500;
    font-size: 24px;
    transition: color 0.2s;
}

.profile__logout:hover {
    color: var(--main-color);
}

.profile__block_info {
    display: flex;
    gap: 10px;
    width: 100%;
    margin-top: 10px;
}

.profile__block {
    border-radius: 15px;
    background: #21222A;
    width: 100%;
    padding: 15px;
}

.profile__block_rank {
    width: 100%;
}

.profile__block_social {
    flex: 0 0 25%
}

.profile__block_balance {
    flex: 0 0 25%
}

.profile__title_block {
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
}

.profile__block_wallet {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.profile__balance {
    font-weight: 700;
    font-size: 21px;
    color: var(--main-color);
}

.profile__block_info {
    display: flex;
}

.profile__user {
    display: flex;
    justify-content: space-between;
    margin-top: 50px;
}

.profile__personal_info {
    display: flex;
    justify-content: space-between;
}

.profile__username {
    font-style: normal;
    font-weight: 500;
    font-size: 32px;
    color: #FFFFFF;
}

.profile__avatar {
    border-radius: 100px;
    width: 125px;
    height: 125px;
}

.profile__user_info {
    position: relative;
    display: flex;
    gap: 15px;
    align-items: center;
}

.profile__user_id {
    display: flex;
    margin-top: 10px;
}

.input__user_id {
    padding-left: 15px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    width: 150px;
    height: 46px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    outline: 0;
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 29px;
    color: #4E4E4E;
    border-color: transparent;
}

.user__copy_id {
    height: 50px;
    background: linear-gradient(102.34deg, rgba(217, 217, 217, 0.0705) -164.91%, rgba(217, 217, 217, 0.012) -60.22%);
    backdrop-filter: blur(250px);
    width: 50px;
    color: var(--main-color);
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.block__user_info {
    background: #21222A;
    border-radius: 15px;
    width: 290px;
    height: 100%;
    padding: 15px;
}

.block__user_inf {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.block__user_inf:last-child {
    margin-bottom: 0;
}

.block_name_profile {
    color: #878787;
    font-weight: 500;
    font-size: 18px;
}

.block__kol_profile {
    font-weight: 600;
    font-size: 18px;
    color: #FFFFFF;
}

.binds__user {
    display: flex;
    gap: 5px;
}

.bind__user_vk {
    width: 36px;
    height: 36px;
    background-color: #0077FF;
    border-radius: 5px;
    font-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
}

.bind__user_tg {
    width: 36px;
    height: 36px;
    background-color: #36B8FF;
    border-radius: 5px;
    font-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
}

.politic-head {
    width: 100%;
    height: 100%;
    color: #a9a9a9;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    white-space: normal;
    background-color: var(--color-inner);
    border-radius: 6px;
    overflow: auto;
    margin-bottom: 50px;
}

.politic-head br {
    margin-bottom: 10px;
}

.politic-name {
    background: #21222A;
    padding: 10px;
    width: 100%;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.politic-head .rules {
    margin-left: 15px;
}

.politic-head span {
    font-size: 21px;
    font-weight: 700;
}

.politic-head p {
    font-size: 18px;
    margin-top: 10px;
}

.pod-paragraph {
    margin-left: 15px;
    margin-top: 0;
}

.profile__wallet_button {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
}

.profile__wallet_button a {
    border: 1px solid #383838;
    background-color: transparent;
    border-radius: 30px;
    width: 45px;
    height: 45px;
    font-weight: 700;
    font-size: 14px;
    color: #2b2b2b;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.profile__wallet_button a:hover {
    background-color: rgba(56, 56, 56, 0.5);
}

.profile__wallet_button a:first-child svg {
    transform: rotate(90deg);
    color: var(--main-color);
}

.profile__wallet_button a:last-child svg {
    transform: rotate(-90deg);
    color: #db2222;
}

.wheel__row {
    position: absolute;
    width: 350px;
    height: 350px;
    z-index: 1;
    -webkit-transition: 13s;
    -o-transition: 13s;
    transition: 13s;
    pointer-events: none;
}

.wheel__row img {
    width: 100%;
    height: auto;
}

.jackpot__field_wrapper {
    position: relative;
    padding: 25px;
    padding-top: 45px;
}

.players__jackpot {
    width: 100%;
    max-width: 885px;
    text-align: center;
    font-size: 0;
    margin-bottom: 12px;
    height: 130px;
    white-space: nowrap;
    overflow: hidden;
}

.player__avatar_card {
    position: relative;
    display: inline-block;
    background: #22252F;
    border-radius: 6px;
    margin: 0 5px;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 130px;
}

.avatar__jackpot {
    width: 55px!important;
    height: 55px!important;
    border-radius: 50%;
    border: 1px solid #5f5f5f;
    margin: 8px 17px;
    padding: 3px;
}

.player__jackpot_bet {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: var(--main-color);
}

.player__jackpot_ticket {
    margin-top: 5px;
    color: #fff;
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    display: flex;
    display: block;
}

.jackpot__timer {
    margin: 0 auto;
    margin-top: 15px;
    width: 200px;
    height: 75px;
    background: #2A2C37;
    border-radius: 10px;
}

.jackpot__bank {
    display: flex;
    justify-content: center;
    margin-top: 5px;
}

.jackpot__time {
    font-weight: 800;
    font-size: 40px;
    line-height: 36px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
}

.jackpot__bank_title, .jackpot__bank_game {
    font-weight: 500;
    font-size: 24px;
    line-height: 36px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
}

.jackpot__bank_game {
    margin-left: 5px;
    color: var(--main-color);
}

.jackpot__roll {
    border-radius: 6px;
    height: 95px;
    text-align: center;
    overflow: hidden;
    margin-top: 15px;
    position: relative;
    background: #2A2C37;
}

.jackpot__roll:before {
    content: "";
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    height: 95px;
    background: #2AC800;
    border-radius: 100px;
    display: block;
    z-index: 1;
    position: absolute
}

.jackpot__roll .inbox {
    left: 50%;
    position: absolute;
    top: 6px;
    perspective: 1000px;
    -webkit-perspective: 1000px;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transform-style: preserve-3d;
    transition: none;
    transform: translateY(0);
    opacity: 1;
}

.jackpot__roll .inbox .jackpot__players {
    display: flex;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transform-style: preserve-3d;
    text-align: center;
    white-space: nowrap;
}

.jackpot__roll .inbox .jackpot__players img {
    border-radius: 10px;
    width: 85px;
    height: 85px;
    vertical-align: top;
    -o-object-fit: cover;
    object-fit: cover;
    margin-right: 3px;
}

.players__wrapper {
    position: relative;
}

.jackpot__winner {
    width: 90%;
    height: 135px;
    text-align: center;
    border-radius: 10px;
    border-width: 2px;
    border-color: var(--main-color);
    padding: 30px 40px 9px;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    position: absolute;
    z-index: 50;
    background: rgba(27, 28, 36, 0.7);;
    padding: 20px 40px;
    border-radius: 12px;
    border: 2px solid #44d13a;
    display: none;
}

.winner__title {
    font-weight: 500;
    position: absolute;
    top: -23%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--main-color);
    color: #fff;
    padding: 5px 20px;
    font-size: 16px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}

.winner__username {
    font-weight: 500;
    font-size: 24px;
    margin-bottom: 3px;
    color: #fff;
}

.winner__sum_title {
    color: #898989;
    margin-top: 3px;
    font-size: 18px;
}

.winner__sum {
    color: var(--main-color);
    text-shadow: 0 0 7px #2ac800, 0 0 25px #2ac800;
    margin-top: 3px;
    font-size: 18px;
}

.winner__chance {
    color: #FFFFFF;
    margin-top: 3px;
    font-size: 18px;
}

.profile__user_rank {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    max-width: 100%;
    margin: auto;
}

.profile__rank_icon {
    width: 60px;
}

.user__rank_icon {
    position: absolute;
    border: none;
    width: 95px;
    left: 55px;
    top: -30px;
}

.crash__bet_user_rank {
    position: absolute;
    border: none;
    width: 55px;
    left: 15px;
    top: -15px;
}

.avatar__jackpot_rank {
    position: absolute;
    border: none;
    width: 55px;
    left: 40px;
    top: -10px;
}

.profile__progress_rank {
    width: 100%;
}

.pay__progress {
    width: 100%;
    border-radius: 3px;
    padding: 3px;
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    position: relative;
    z-index: 5;
    color: #fff;
    background: linear-gradient(to right,rgba(42, 200, 0, 0.2),rgba(142, 163, 64, 0.2));
}

.pay__progress, .user__sum_pay, .user__next_sumpay {
    font-weight: 700;
    color: #fff;
}

.progress__fill {
    background: linear-gradient(to right,rgba(42, 200, 0, 1),rgba(142, 163, 64, 1));
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 3px;
    z-index: -1;
}

.bonus__promo_x {
    display: flex;
    gap: 5px;
    padding: 2px 10px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 19px;
    color: #FFFFFF;
}

.bonus__promo_group {
    display: flex;
    flex-direction: column;
}

.rank__title {
    display: flex;
    justify-content: space-between;
}

.rank__next_bonus {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 2px 10px;
    background: linear-gradient(45.87deg, #6F7DFF 6.6%, #8729FF 102.94%);;
    border-radius: 6px;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 19px;
    color: #FFFFFF;
}

.leftside__online {
    margin-top: 5px;
    margin-bottom: 5px;
}

.site__online {
    text-align: center;
    width: 100%;
    display: block;
    color: #a9a9a9;
}

.site__online .online {
    font-weight: 500;
    font-size: 14px;
}

.online__dot {
    margin-right: 6px;
    width: 7px;
    display: inline-block;
    height: 7px;
    border-radius: 50%;
    background: var(--main-color);
    -webkit-box-shadow: 0 0 4px 2px rgb(42 200 0 / 50%);
    box-shadow: 0 0 4px 2px rgb(42 200 0 / 50%);
}

.game__crash {
    flex: auto;
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.crash__block {
    font-weight: 700;
    margin: 0 auto;
    width: 100%;
    display: -ms-flexbox;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    padding: 5px 5px 5px 0;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    height: 425px;
}

.crash__block .crash {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    width: 100%;
}

.crash__block .crash .chart {
    position: relative;
    width: 100%;
    height: 100%!important;
}

.game__crash .crash__block .crash .chart {
    position: relative;
    width: 100%;
    height: 100%!important;
}

.flot-overlay {
    position: relative;
    width: 100%!important;
    height: 100%!important;
}

.chart-info {
    display: -ms-flexbox;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -ms-flex-align: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -ms-flex-pack: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    position: absolute;
    text-align: center;
    font-weight: 800;
    font-size: 40px;
    color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 30px;
}

.crash__history {
    margin-top: 10px;
}

.crash__bets {
    min-height: 150px;
}

.crash__bet {
    display: flex;
    align-items: center;
    margin-bottom: 7px;
    border-radius: 15px;
    background: var(--color-inner);
}

.crash__bet_user_wr {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.crash__bet_values {
    display: flex;
    margin-left: auto;
    color: #fff;
    align-items: center;
    margin-right: 15px;
}

.crash__bet_user_image_wr {
    position: relative;
    height: 50px;
    width: 50px;
    margin-right: 10px;
}

.crash__bet_user_image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    padding: 1px;
}

.crash__bet_username_wr {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 230px;
    color: #fff;
}

.crash__bet_coef {
    padding: 7px 15px;
    margin-right: 15px;
    width: 100px;
    text-align: center;
    background: #20222b;
    border-radius: 5px;
    border: 2px solid #1E1F27;
}

.crash__bet_sum {
    padding: 8px 15px;
    margin-right: 7px;
    color: var(--main-color);
    font-weight: 700;
}

.crash__bet_value {
    white-space: nowrap;
    font-weight: 600;
    font-size: 15px;
}

.crash__bet_win {
    padding: 7px 15px;
    margin-right: 0;
    border: 3px solid var(--main-color);
    color: var(--main-color);
    border-radius: 5px;
    font-weight: 700;
    width: 100px;
    text-align: center;
}

.crash__bet_win_hidden {
    visibility: hidden;
    opacity: 0;
}

.withdraws__inputs {
    display: flex;
    gap: 10px;
}

.withdraw__input_wr {
    width: 100%;
}

.other_auth_modal {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFF;
    font-size: 14px;
    font-weight: 500;
    gap: 5px;
    margin-top: 10px;
}

.other_method_auth {
    color: var(--main-color);
    cursor: pointer;
}

.register_modal_auth, .login_modal_auth {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

@media (max-width: 780px) {
    .mobile_menu {
        display: block;
    }
    .fix__left_nav {
        display: none;
    }
    .navmenu {
        display: none;
    }
    .games__grid {
        grid-template-columns: repeat(auto-fill,minmax(165px,1fr));
        grid-gap: 7px;
        padding: 15px;
    }
    .main__content {
        top: 90px;
        padding-left: 15px;
        padding: 0 8px 55px!important;
    }
    .bonus__grid {
        grid-template-columns: repeat(auto-fill,minmax(100%,2fr));
        padding: 15px;
    }
    .bonus__daily {
        padding: 15px 20px;
        gap: 5px;
    }
    .bonus__vk {
        padding: 15px 20px;
        gap: 5px;
    }
    .bonus__promo {
        padding: 15px 20px;
        gap: 5px;
    }
    .bonus__tg {
        padding: 15px 20px;
        gap: 5px;
    }
    .footer {
        display: none;
    }
    .modal__wallet_body {
        flex-direction: column;
    }
    .modal__wallet_body_paysys, .modal__wallet_body_pay {
        width: 100%;
        padding-right: 0;
    }
    .modal__wallet_body_pay {
        padding-left: 0;
    }
    .profile__block_info {
        flex-direction: column;
    }
    .refferals__info {
        flex-direction: column;
    }
    .referrals__system {
        min-width: 100%;
    }
    .games__sidebar {
        width: 100%;
        max-width: 100%;
    }
}
@media (max-width: 991px) {
    .games__grid {
        grid-template-columns: repeat(auto-fill,minmax(220px,1fr));
    }
    .games__area {
        flex-wrap: wrap;
        gap: 5px;
    }
    .games__sidebar {
        order: 2;
    }
}

@media (max-width: 500px) {
    .games__area_field {
        padding: 10px 10px;
    }
    .user__rank_icon {
        position: absolute;
        border: none;
        width: 85px;
        left: 50px;
        top: -25px;
    }
    .mines__field {
        width: 270px;
    }
    .mines__cell {
        margin: 2px;
        width: 50px;
        height: 50px;
    }
    .mines__cell .cristal, .mines__cell .bomb {
        width: 100%;
        height: 100%;
    }
    .games__sidebar {
        min-height: 100%;
        border-radius: 15px;
    }
    .mines_coef {
        padding: 10px 15px;
    }
    .ruble {
        display: none;
    }
    .games__field {
        min-height: 450px;
        border-radius: 15px;
    }
    .no_mobile {
        display: none;
    }
    .history__table {
        width: 100%;
        --table-cols: 20% 20% 30% 30%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    .modal__wallet_header_item i {
        display: none;
    }
    .modal__wallet_header_nav {
        justify-content: space-between;
    }
    .history__type {
        width: 100%;
        font-size: 21px;
    }
    .navbar {
        height: 55px;
    }
    .main__content {
        top: 70px;
    }
    .dice__possible_win {
        font-size: 85px;
    }
    .dice__play_buttons {
        gap: 5px;
    }
    .dice__play {
        padding: 15px 50px;
    }
    .dice__result {
        margin-bottom: 10px;
    }
    .profile__user {
        flex-direction: column;
    }
    .block__user_info  {
        margin-top: 20px;
    }
    .profile__username {
        font-size: 21px;
    }
    .profile__user_info {
        gap: 5px;
    }
    .profile__avatar {
        width: 110px;
        height: 110px;
    }
    .input__user_id {
        width: 100%;
    }
    .profile__name, .profile__logout {
        font-size: 18px;
    }
    .games__grid {
        grid-template-columns: repeat(auto-fill,minmax(180px,1fr));
    }
    .bonus__inner {
        flex-direction: column;
    }
    .wheel__colors_history {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    .wheel__history_color {
        margin-right: 0;
    }
    .game__wheel {
        width: 290px;
        height: 290px;
    }
    .wheel__row {
        width: 290px;
        height: 290px;
    }
}

@media (max-width: 400px) {
    .modal__wallet_header_item {
        font-size: 14px;
    }
    .history__type {
        font-size: 18px;
    }
    .small__mobile_no {
        display: none;
    }
    .history__table>thead>tr>td {
        font-size: 16px;
    }
    .history__table>tbody>tr>td {
        font-size: 16px;
    }
    .history__table {
        --table-cols: 15% 28% 27% 30%;
    }
    .method__img {
        width: 32px;
        height: 32px;
    }
}