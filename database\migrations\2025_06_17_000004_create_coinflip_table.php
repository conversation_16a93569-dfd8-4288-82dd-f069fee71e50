<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coinflip', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->enum('choice', ['heads', 'tails']);
            $table->enum('result', ['heads', 'tails']);
            $table->enum('status', ['win', 'lose']);
            $table->decimal('win', 10, 2)->default(0);
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coinflip');
    }
};
