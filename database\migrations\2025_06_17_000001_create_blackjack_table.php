<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('blackjack', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->json('player_cards');
            $table->json('dealer_cards');
            $table->integer('player_total')->default(0);
            $table->integer('dealer_total')->default(0);
            $table->enum('status', ['playing', 'player_win', 'dealer_win', 'push', 'blackjack', 'bust'])->default('playing');
            $table->decimal('win', 10, 2)->default(0);
            $table->boolean('can_double')->default(true);
            $table->boolean('can_split')->default(false);
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('blackjack');
    }
};
