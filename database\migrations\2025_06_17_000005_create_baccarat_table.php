<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('baccarat', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('bet', 10, 2);
            $table->enum('choice', ['player', 'banker', 'tie']);
            $table->json('player_cards');
            $table->json('banker_cards');
            $table->integer('player_total');
            $table->integer('banker_total');
            $table->enum('winner', ['player', 'banker', 'tie']);
            $table->enum('status', ['win', 'lose']);
            $table->decimal('win', 10, 2)->default(0);
            $table->decimal('payout_multiplier', 5, 2)->default(0);
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('status');
            $table->index('winner');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('baccarat');
    }
};
