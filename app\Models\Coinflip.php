<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coinflip extends Model
{
    use HasFactory;

    protected $table = 'coinflip';

    protected $fillable = [
        'user_id', 'bet', 'choice', 'result', 'status', 'win'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Flip a coin and return the result
     */
    public static function flipCoin()
    {
        return rand(0, 1) ? 'heads' : 'tails';
    }

    /**
     * Get the opposite side of the coin
     */
    public static function getOpposite($side)
    {
        return $side === 'heads' ? 'tails' : 'heads';
    }

    /**
     * Get display name for coin side
     */
    public static function getDisplayName($side)
    {
        return $side === 'heads' ? 'Орёл' : 'Решка';
    }
}
