<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\User;

class GuestMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // If user is already authenticated, continue normally
        if (Auth::check()) {
            return $next($request);
        }

        // Create or get guest user session
        if (!Session::has('guest_user')) {
            $guestUser = $this->createGuestUser();
            Session::put('guest_user', $guestUser);
        } else {
            $guestUser = Session::get('guest_user');
        }

        // Manually authenticate the guest user for this request
        Auth::login($guestUser);

        return $next($request);
    }

    /**
     * Create a temporary guest user
     */
    private function createGuestUser()
    {
        // Create a virtual user object (not saved to database)
        $guestUser = new User();
        $guestUser->id = 999999; // High ID to avoid conflicts
        $guestUser->username = 'Guest_' . uniqid();
        $guestUser->email = '<EMAIL>';
        $guestUser->balance = 10000.00; // Demo balance
        $guestUser->admin = 0;
        $guestUser->banned = 0;
        $guestUser->avatar = 'default.png';
        $guestUser->ref_id = null;
        $guestUser->ref_money = 0;
        $guestUser->wager = 0;
        $guestUser->created_at = now();
        $guestUser->updated_at = now();
        
        // Mark as guest user
        $guestUser->is_guest = true;
        
        return $guestUser;
    }
}
