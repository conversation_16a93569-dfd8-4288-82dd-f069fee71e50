@extends('layout')

@section('content')
<div class="coinflip__container">
    <div class="games__area">
        <div class="games__sidebar">
            <div class="games__input_wrapper_bet">
                <label class="games__sidebar_label">Bet Amount</label>
                <div class="games__sidebar_wrapper_input">
                    <input type="number" class="games__sidebar_input input__bet" value="10" min="1">
                </div>
                <div class="games__sidebar_help_bombs">
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(1)">+1</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(10)">+10</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(100)">+100</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('min')">Min</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('max')">Max</button>
                </div>
            </div>
            
            <!-- Choice Buttons -->
            <div class="coinflip__choice">
                <h4>Choose Side</h4>
                <button class="coinflip__choice_btn heads" id="choice-heads" onclick="selectChoice('heads')">
                    <div class="coinflip__choice_icon">🦅</div>
                    <div class="coinflip__choice_text">Heads</div>
                </button>
                <button class="coinflip__choice_btn tails" id="choice-tails" onclick="selectChoice('tails')">
                    <div class="coinflip__choice_icon">👑</div>
                    <div class="coinflip__choice_text">Tails</div>
                </button>
            </div>
            
            <div class="coinflip__flip_wrapper">
                <button class="coinflip__flip_btn" id="flip-btn" disabled>Flip Coin</button>
            </div>
            
            <!-- Statistics -->
            <div class="coinflip__stats">
                <h4>Статистика</h4>
                <div class="coinflip__stats_item">
                    <span>Орёл:</span>
                    <span id="heads-count">0</span>
                </div>
                <div class="coinflip__stats_item">
                    <span>Решка:</span>
                    <span id="tails-count">0</span>
                </div>
                <div class="coinflip__stats_item">
                    <span>Всего игр:</span>
                    <span id="total-games">0</span>
                </div>
            </div>
        </div>
        
        <div class="coinflip__field">
            <!-- Coin Animation Area -->
            <div class="coinflip__animation">
                <div class="coinflip__coin" id="coin">
                    <div class="coinflip__coin_side heads">
                        <div class="coinflip__coin_content">
                            <div class="coinflip__coin_icon">🦅</div>
                            <div class="coinflip__coin_text">ОРЁЛ</div>
                        </div>
                    </div>
                    <div class="coinflip__coin_side tails">
                        <div class="coinflip__coin_content">
                            <div class="coinflip__coin_icon">👑</div>
                            <div class="coinflip__coin_text">РЕШКА</div>
                        </div>
                    </div>
                </div>
                
                <div class="coinflip__result" id="game-result">
                    <div class="coinflip__result_text">Выберите сторону и сделайте ставку</div>
                </div>
            </div>
            
            <!-- Recent Results -->
            <div class="coinflip__recent">
                <h4>Последние результаты</h4>
                <div class="coinflip__recent_list" id="recent-results">
                    <!-- Recent results will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game History -->
    <div class="coinflip__history">
        <h4>История ваших игр</h4>
        <div class="coinflip__history_list" id="game-history">
            <!-- Game history will be populated here -->
        </div>
    </div>
    
    <script src="/assets/js/coinflip.js"></script>
</div>

<style>
.coinflip__container {
    padding: 20px;
}

.coinflip__field {
    flex: 1;
    margin-left: 20px;
}

.coinflip__animation {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #1a4d3a, #2d5a3d);
    border-radius: 15px;
    border: 3px solid #4a7c59;
    margin-bottom: 30px;
}

.coinflip__coin {
    width: 150px;
    height: 150px;
    margin: 0 auto 30px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 2s ease-in-out;
}

.coinflip__coin_side {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backface-visibility: hidden;
    border: 4px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.coinflip__coin_side.heads {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8B4513;
}

.coinflip__coin_side.tails {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #4a4a4a;
    transform: rotateY(180deg);
}

.coinflip__coin_content {
    text-align: center;
}

.coinflip__coin_icon {
    font-size: 40px;
    margin-bottom: 10px;
}

.coinflip__coin_text {
    font-size: 16px;
    font-weight: bold;
}

.coinflip__coin.flipping {
    animation: coinFlip 2s ease-in-out;
}

@keyframes coinFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(1800deg) rotateX(360deg); }
    100% { transform: rotateY(1800deg); }
}

.coinflip__coin.heads-result {
    transform: rotateY(0deg);
}

.coinflip__coin.tails-result {
    transform: rotateY(180deg);
}

.coinflip__result {
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.coinflip__result_text {
    font-size: 18px;
    font-weight: bold;
}

.coinflip__choice {
    margin: 20px 0;
}

.coinflip__choice h4 {
    color: #fff;
    margin-bottom: 15px;
}

.coinflip__choice_btn {
    display: block;
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    border: 3px solid transparent;
    border-radius: 10px;
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.coinflip__choice_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.coinflip__choice_btn.selected {
    border-color: #ffd700;
    background: linear-gradient(135deg, #ffc107, #e0a800);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
}

.coinflip__choice_btn.heads.selected {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8B4513;
}

.coinflip__choice_btn.tails.selected {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #4a4a4a;
}

.coinflip__choice_icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.coinflip__choice_text {
    font-size: 16px;
    font-weight: bold;
}

.coinflip__flip_btn {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.coinflip__flip_btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-2px);
}

.coinflip__flip_btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.coinflip__stats {
    margin-top: 30px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 8px;
}

.coinflip__stats h4 {
    color: #fff;
    margin-bottom: 15px;
}

.coinflip__stats_item {
    display: flex;
    justify-content: space-between;
    color: #fff;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.coinflip__recent {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.coinflip__recent h4 {
    color: #fff;
    margin-bottom: 15px;
}

.coinflip__recent_list {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.coinflip__recent_item {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid #ffd700;
}

.coinflip__recent_item.heads {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8B4513;
}

.coinflip__recent_item.tails {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #4a4a4a;
}

.coinflip__history {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.coinflip__history h4 {
    color: #fff;
    margin-bottom: 15px;
}

.coinflip__history_item {
    background: rgba(255,255,255,0.1);
    padding: 12px;
    margin: 8px 0;
    border-radius: 8px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coinflip__history_item.win {
    border-left: 4px solid #28a745;
}

.coinflip__history_item.lose {
    border-left: 4px solid #dc3545;
}

.coinflip__history_details {
    display: flex;
    gap: 15px;
    align-items: center;
}

.coinflip__history_choice {
    font-weight: bold;
}

.coinflip__history_result {
    font-size: 14px;
    opacity: 0.8;
}

.coinflip__history_amount {
    font-weight: bold;
}

.win .coinflip__history_amount { color: #28a745; }
.lose .coinflip__history_amount { color: #dc3545; }
</style>
@endsection
