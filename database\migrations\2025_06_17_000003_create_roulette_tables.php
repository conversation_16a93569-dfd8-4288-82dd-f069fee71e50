<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Roulette games table
        Schema::create('roulette', function (Blueprint $table) {
            $table->id();
            $table->integer('winning_number');
            $table->enum('status', ['betting', 'spinning', 'finished'])->default('betting');
            $table->decimal('total_bets', 10, 2)->default(0);
            $table->timestamps();
        });

        // Roulette bets table
        Schema::create('roulette_bets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->bigInteger('game_id');
            $table->string('bet_type'); // 'straight', 'split', 'red', 'black', 'odd', 'even', etc.
            $table->string('bet_value'); // number, color, or range
            $table->decimal('amount', 10, 2);
            $table->decimal('win', 10, 2)->default(0);
            $table->decimal('payout_multiplier', 5, 2)->default(0);
            $table->boolean('is_winner')->default(false);
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('game_id');
            $table->index('bet_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('roulette_bets');
        Schema::dropIfExists('roulette');
    }
};
