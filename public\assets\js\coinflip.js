$(document).ready(function() {
    let selectedChoice = null;
    let isFlipping = false;
    let stats = { heads: 0, tails: 0, total: 0 };
    
    // Initialize
    loadGameHistory();
    loadRecentResults();
    loadStats();
    
    // Bet adjustment functions
    window.adjustBet = function(amount) {
        const currentBet = parseFloat($('.input__bet').val()) || 0;
        $('.input__bet').val(Math.max(1, currentBet + amount));
    };
    
    window.setBet = function(type) {
        if (type === 'min') {
            $('.input__bet').val(1);
        } else if (type === 'max') {
            const balance = parseFloat($('#balance').text()) || 0;
            $('.input__bet').val(balance);
        }
    };
    
    // Choice selection
    window.selectChoice = function(choice) {
        if (isFlipping) return;
        
        selectedChoice = choice;
        
        // Update UI
        $('.coinflip__choice_btn').removeClass('selected');
        $(`#choice-${choice}`).addClass('selected');
        
        // Enable flip button
        $('#flip-btn').prop('disabled', false);
        
        updateResultText(`Selected: ${choice === 'heads' ? 'Heads' : 'Tails'}. Click "Flip Coin"`);
    };
    
    // Flip button click
    $('#flip-btn').click(function() {
        if (!selectedChoice || isFlipping) return;
        
        const bet = parseFloat($('.input__bet').val());
        
        if (!bet || bet < 1) {
            showNotification('Enter a valid bet amount', 'error');
            return;
        }
        
        flipCoin(bet, selectedChoice);
    });
    
    function flipCoin(bet, choice) {
        isFlipping = true;
        $('#flip-btn').prop('disabled', true);
        $('.coinflip__choice_btn').prop('disabled', true);
        
        updateResultText('Coin is in the air...');
        
        // Start coin animation
        const coin = $('#coin');
        coin.removeClass('heads-result tails-result').addClass('flipping');
        
        // Make API call
        $.post('/coinflip/flip', {
            _token: $('meta[name="csrf-token"]').attr('content'),
            bet: bet,
            choice: choice
        }).then(response => {
            if (response.type === 'success') {
                // Wait for animation to complete
                setTimeout(() => {
                    showResult(response);
                }, 2000);
            } else {
                showNotification(response.msg, 'error');
                resetGame();
            }
        }).catch(error => {
            showNotification('Ошибка при игре', 'error');
            resetGame();
        });
    }
    
    function showResult(response) {
        const coin = $('#coin');
        coin.removeClass('flipping');
        
        // Set final coin position
        if (response.result === 'heads') {
            coin.addClass('heads-result');
        } else {
            coin.addClass('tails-result');
        }
        
        // Update result text
        let resultText = `Result: ${response.result_display}`;
        if (response.is_win) {
            resultText += ` - You won $${response.win_amount}!`;
            showNotification(`Congratulations! You won $${response.win_amount}!`, 'win');
        } else {
            resultText += ` - You lost $${response.game.bet}`;
            showNotification(`Better luck next time!`, 'lose');
        }
        
        updateResultText(resultText);
        updateBalance(response.balance);
        
        // Update stats and history
        updateStats(response.result);
        addToHistory(response.game);
        addToRecentResults(response.result);
        
        // Reset game after delay
        setTimeout(() => {
            resetGame();
        }, 3000);
    }
    
    function resetGame() {
        isFlipping = false;
        selectedChoice = null;
        
        $('.coinflip__choice_btn').removeClass('selected').prop('disabled', false);
        $('#flip-btn').prop('disabled', true);
        
        updateResultText('Choose a side and place your bet');
    }
    
    function updateResultText(text) {
        $('#game-result .coinflip__result_text').text(text);
    }
    
    function updateStats(result) {
        stats[result]++;
        stats.total++;
        
        $('#heads-count').text(stats.heads);
        $('#tails-count').text(stats.tails);
        $('#total-games').text(stats.total);
    }
    
    function loadStats() {
        // Calculate stats from recent results
        $.get('/coinflip/recent-results').then(response => {
            if (response.type === 'success') {
                stats = { heads: 0, tails: 0, total: 0 };
                
                response.results.forEach(result => {
                    stats[result.result]++;
                    stats.total++;
                });
                
                $('#heads-count').text(stats.heads);
                $('#tails-count').text(stats.tails);
                $('#total-games').text(stats.total);
            }
        });
    }
    
    function loadGameHistory() {
        $.get('/coinflip/history').then(response => {
            if (response.type === 'success') {
                displayGameHistory(response.history);
            }
        });
    }
    
    function loadRecentResults() {
        $.get('/coinflip/recent-results').then(response => {
            if (response.type === 'success') {
                displayRecentResults(response.results.slice(0, 20));
            }
        });
    }
    
    function displayGameHistory(history) {
        const container = $('#game-history');
        container.empty();
        
        history.forEach(game => {
            const historyItem = `
                <div class="coinflip__history_item ${game.status}">
                    <div class="coinflip__history_details">
                        <div class="coinflip__history_choice">
                            Выбор: ${game.choice_display}
                        </div>
                        <div class="coinflip__history_result">
                            Результат: ${game.result_display}
                        </div>
                    </div>
                    <div class="coinflip__history_amount">
                        ${game.status === 'win' ? '+' : '-'}${game.status === 'win' ? game.win : game.bet} ₽
                    </div>
                </div>
            `;
            container.append(historyItem);
        });
    }
    
    function displayRecentResults(results) {
        const container = $('#recent-results');
        container.empty();
        
        results.forEach(result => {
            const resultItem = `
                <div class="coinflip__recent_item ${result.result}">
                    ${result.result === 'heads' ? '🦅' : '👑'}
                </div>
            `;
            container.append(resultItem);
        });
    }
    
    function addToHistory(game) {
        const historyItem = `
            <div class="coinflip__history_item ${game.status}">
                <div class="coinflip__history_details">
                    <div class="coinflip__history_choice">
                        Выбор: ${game.choice === 'heads' ? 'Орёл' : 'Решка'}
                    </div>
                    <div class="coinflip__history_result">
                        Результат: ${game.result === 'heads' ? 'Орёл' : 'Решка'}
                    </div>
                </div>
                <div class="coinflip__history_amount">
                    ${game.status === 'win' ? '+' : '-'}${game.status === 'win' ? game.win : game.bet} ₽
                </div>
            </div>
        `;
        
        $('#game-history').prepend(historyItem);
        
        // Keep only last 10 items
        $('#game-history .coinflip__history_item').slice(10).remove();
    }
    
    function addToRecentResults(result) {
        const resultItem = `
            <div class="coinflip__recent_item ${result}">
                ${result === 'heads' ? '🦅' : '👑'}
            </div>
        `;
        
        $('#recent-results').prepend(resultItem);
        
        // Keep only last 20 items
        $('#recent-results .coinflip__recent_item').slice(20).remove();
    }
    
    function showNotification(message, type) {
        // Create notification
        const notification = $(`
            <div class="coinflip__notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 3000);
    }
    
    function updateBalance(newBalance) {
        $('#balance').text(newBalance.toFixed(2));
    }
});

// Additional CSS for notifications
$('<style>').text(`
    .coinflip__notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }
    
    .coinflip__notification.error { background: #dc3545; }
    .coinflip__notification.win { background: #28a745; }
    .coinflip__notification.lose { background: #6c757d; }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`).appendTo('head');
