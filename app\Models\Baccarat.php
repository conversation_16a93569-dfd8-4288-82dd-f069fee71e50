<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Baccarat extends Model
{
    use HasFactory;

    protected $table = 'baccarat';

    protected $fillable = [
        'user_id', 'bet', 'choice', 'player_cards', 'banker_cards',
        'player_total', 'banker_total', 'winner', 'status', 'win', 'payout_multiplier'
    ];

    protected $casts = [
        'player_cards' => 'array',
        'banker_cards' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate baccarat hand total
     */
    public static function calculateHandTotal($cards)
    {
        $total = 0;
        
        foreach ($cards as $card) {
            $value = self::getCardValue($card);
            $total += $value;
        }
        
        // In baccarat, only the last digit matters
        return $total % 10;
    }

    /**
     * Get card value for baccarat
     */
    public static function getCardValue($card)
    {
        $rank = $card['rank'];
        
        if (in_array($rank, ['J', 'Q', 'K'])) {
            return 0; // Face cards are worth 0 in baccarat
        } elseif ($rank == 'A') {
            return 1; // Ace is worth 1
        } else {
            return (int) $rank; // Number cards are face value
        }
    }

    /**
     * Generate a new deck of cards
     */
    public static function generateDeck()
    {
        $suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        $ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        $deck = [];

        foreach ($suits as $suit) {
            foreach ($ranks as $rank) {
                $deck[] = ['suit' => $suit, 'rank' => $rank];
            }
        }

        shuffle($deck);
        return $deck;
    }

    /**
     * Determine if player needs third card
     */
    public static function playerNeedsThirdCard($playerTotal)
    {
        return $playerTotal <= 5;
    }

    /**
     * Determine if banker needs third card
     */
    public static function bankerNeedsThirdCard($bankerTotal, $playerTotal, $playerThirdCard = null)
    {
        // If player stood (no third card)
        if ($playerThirdCard === null) {
            return $bankerTotal <= 5;
        }

        // Banker drawing rules based on player's third card
        switch ($bankerTotal) {
            case 0:
            case 1:
            case 2:
                return true;
            case 3:
                return $playerThirdCard != 8;
            case 4:
                return in_array($playerThirdCard, [2, 3, 4, 5, 6, 7]);
            case 5:
                return in_array($playerThirdCard, [4, 5, 6, 7]);
            case 6:
                return in_array($playerThirdCard, [6, 7]);
            case 7:
                return false;
            default:
                return false;
        }
    }

    /**
     * Get payout multiplier for bet type
     */
    public static function getPayoutMultiplier($choice)
    {
        switch ($choice) {
            case 'player':
                return 1; // 1:1
            case 'banker':
                return 0.95; // 1:1 minus 5% commission
            case 'tie':
                return 8; // 8:1
            default:
                return 0;
        }
    }

    /**
     * Get display name for choice
     */
    public static function getDisplayName($choice)
    {
        switch ($choice) {
            case 'player':
                return 'Игрок';
            case 'banker':
                return 'Банкир';
            case 'tie':
                return 'Ничья';
            default:
                return $choice;
        }
    }

    /**
     * Determine the winner of a baccarat hand
     */
    public static function determineWinner($playerTotal, $bankerTotal)
    {
        if ($playerTotal > $bankerTotal) {
            return 'player';
        } elseif ($bankerTotal > $playerTotal) {
            return 'banker';
        } else {
            return 'tie';
        }
    }
}
