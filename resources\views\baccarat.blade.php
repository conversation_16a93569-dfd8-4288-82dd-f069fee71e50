@extends('layout')

@section('content')
<div class="baccarat__container">
    <div class="games__area">
        <div class="games__sidebar">
            <div class="games__input_wrapper_bet">
                <label class="games__sidebar_label">Ставка</label>
                <div class="games__sidebar_wrapper_input">
                    <input type="number" class="games__sidebar_input input__bet" value="10" min="1">
                </div>
                <div class="games__sidebar_help_bombs">
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(1)">+1</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(10)">+10</button>
                    <button class="games__sidebar_bombs_action" onclick="adjustBet(100)">+100</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('min')">Min</button>
                    <button class="games__sidebar_bombs_action" onclick="setBet('max')">Max</button>
                </div>
            </div>
            
            <!-- Betting Options -->
            <div class="baccarat__betting">
                <h4>Выберите ставку</h4>
                <button class="baccarat__bet_btn player" id="bet-player" onclick="selectBet('player')">
                    <div class="baccarat__bet_title">Игрок</div>
                    <div class="baccarat__bet_payout">1:1</div>
                </button>
                <button class="baccarat__bet_btn banker" id="bet-banker" onclick="selectBet('banker')">
                    <div class="baccarat__bet_title">Банкир</div>
                    <div class="baccarat__bet_payout">1:1 (-5%)</div>
                </button>
                <button class="baccarat__bet_btn tie" id="bet-tie" onclick="selectBet('tie')">
                    <div class="baccarat__bet_title">Ничья</div>
                    <div class="baccarat__bet_payout">8:1</div>
                </button>
            </div>
            
            <div class="baccarat__play_wrapper">
                <button class="baccarat__play_btn" id="play-btn" disabled>Играть</button>
            </div>
            
            <!-- Statistics -->
            <div class="baccarat__stats">
                <h4>Статистика</h4>
                <div class="baccarat__stats_item">
                    <span>Игрок:</span>
                    <span id="player-wins">0</span>
                </div>
                <div class="baccarat__stats_item">
                    <span>Банкир:</span>
                    <span id="banker-wins">0</span>
                </div>
                <div class="baccarat__stats_item">
                    <span>Ничья:</span>
                    <span id="tie-wins">0</span>
                </div>
                <div class="baccarat__stats_item">
                    <span>Всего игр:</span>
                    <span id="total-games">0</span>
                </div>
            </div>
        </div>
        
        <div class="baccarat__field">
            <!-- Game Table -->
            <div class="baccarat__table">
                <!-- Player Section -->
                <div class="baccarat__section player">
                    <div class="baccarat__section_title">Игрок</div>
                    <div class="baccarat__section_total" id="player-total">0</div>
                    <div class="baccarat__section_cards" id="player-cards">
                        <!-- Player cards will be populated here -->
                    </div>
                </div>
                
                <!-- Game Status -->
                <div class="baccarat__status" id="game-status">
                    <div class="baccarat__status_text">Выберите ставку и нажмите "Играть"</div>
                </div>
                
                <!-- Banker Section -->
                <div class="baccarat__section banker">
                    <div class="baccarat__section_title">Банкир</div>
                    <div class="baccarat__section_total" id="banker-total">0</div>
                    <div class="baccarat__section_cards" id="banker-cards">
                        <!-- Banker cards will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Recent Results -->
            <div class="baccarat__recent">
                <h4>Последние результаты</h4>
                <div class="baccarat__recent_list" id="recent-results">
                    <!-- Recent results will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game History -->
    <div class="baccarat__history">
        <h4>История ваших игр</h4>
        <div class="baccarat__history_list" id="game-history">
            <!-- Game history will be populated here -->
        </div>
    </div>
    
    <script src="/assets/js/baccarat.js"></script>
</div>

<style>
.baccarat__container {
    padding: 20px;
}

.baccarat__field {
    flex: 1;
    margin-left: 20px;
}

.baccarat__table {
    background: linear-gradient(135deg, #1a4d3a, #2d5a3d);
    border-radius: 15px;
    padding: 30px;
    min-height: 400px;
    position: relative;
    border: 3px solid #4a7c59;
    margin-bottom: 30px;
}

.baccarat__section {
    text-align: center;
    margin: 20px 0;
}

.baccarat__section.player {
    margin-bottom: 40px;
}

.baccarat__section.banker {
    margin-top: 40px;
}

.baccarat__section_title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10px;
}

.baccarat__section_total {
    font-size: 28px;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 15px;
}

.baccarat__section_cards {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    min-height: 100px;
}

.baccarat__card {
    width: 60px;
    height: 84px;
    background: #fff;
    border-radius: 8px;
    border: 2px solid #333;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px;
    font-weight: bold;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    animation: cardDeal 0.5s ease-out;
}

@keyframes cardDeal {
    from {
        transform: translateY(-50px) rotateY(180deg);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotateY(0deg);
        opacity: 1;
    }
}

.baccarat__card.red {
    color: #d32f2f;
}

.baccarat__card.black {
    color: #333;
}

.baccarat__card_rank {
    font-size: 14px;
    line-height: 1;
}

.baccarat__card_suit {
    font-size: 18px;
    text-align: center;
}

.baccarat__status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 15px 25px;
    border-radius: 10px;
    text-align: center;
    min-width: 250px;
}

.baccarat__status_text {
    font-size: 16px;
    font-weight: bold;
}

.baccarat__betting {
    margin: 20px 0;
}

.baccarat__betting h4 {
    color: #fff;
    margin-bottom: 15px;
}

.baccarat__bet_btn {
    display: block;
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    border: 3px solid transparent;
    border-radius: 10px;
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.baccarat__bet_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.baccarat__bet_btn.selected {
    border-color: #ffd700;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
}

.baccarat__bet_btn.player.selected {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.baccarat__bet_btn.banker.selected {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.baccarat__bet_btn.tie.selected {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.baccarat__bet_title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.baccarat__bet_payout {
    font-size: 14px;
    opacity: 0.8;
}

.baccarat__play_btn {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.baccarat__play_btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-2px);
}

.baccarat__play_btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.baccarat__stats {
    margin-top: 30px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 8px;
}

.baccarat__stats h4 {
    color: #fff;
    margin-bottom: 15px;
}

.baccarat__stats_item {
    display: flex;
    justify-content: space-between;
    color: #fff;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.baccarat__recent {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.baccarat__recent h4 {
    color: #fff;
    margin-bottom: 15px;
}

.baccarat__recent_list {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.baccarat__recent_item {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    border: 2px solid #ffd700;
}

.baccarat__recent_item.player {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.baccarat__recent_item.banker {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.baccarat__recent_item.tie {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.baccarat__history {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.baccarat__history h4 {
    color: #fff;
    margin-bottom: 15px;
}

.baccarat__history_item {
    background: rgba(255,255,255,0.1);
    padding: 12px;
    margin: 8px 0;
    border-radius: 8px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.baccarat__history_item.win {
    border-left: 4px solid #28a745;
}

.baccarat__history_item.lose {
    border-left: 4px solid #dc3545;
}

.baccarat__history_details {
    display: flex;
    gap: 15px;
    align-items: center;
}

.baccarat__history_choice {
    font-weight: bold;
}

.baccarat__history_result {
    font-size: 14px;
    opacity: 0.8;
}

.baccarat__history_amount {
    font-weight: bold;
}

.win .baccarat__history_amount { color: #28a745; }
.lose .baccarat__history_amount { color: #dc3545; }
</style>
@endsection
