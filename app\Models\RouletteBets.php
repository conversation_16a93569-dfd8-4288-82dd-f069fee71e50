<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RouletteBets extends Model
{
    use HasFactory;

    protected $table = 'roulette_bets';

    protected $fillable = [
        'user_id', 'game_id', 'bet_type', 'bet_value', 
        'amount', 'win', 'payout_multiplier', 'is_winner'
    ];

    protected $casts = [
        'is_winner' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function game()
    {
        return $this->belongsTo(Roulette::class, 'game_id');
    }
}
