$(document).ready(function() {
    let selectedBet = null;
    let isPlaying = false;
    let stats = { player: 0, banker: 0, tie: 0, total: 0 };
    
    // Initialize
    loadGameHistory();
    loadRecentResults();
    loadStats();
    
    // Bet adjustment functions
    window.adjustBet = function(amount) {
        const currentBet = parseFloat($('.input__bet').val()) || 0;
        $('.input__bet').val(Math.max(1, currentBet + amount));
    };
    
    window.setBet = function(type) {
        if (type === 'min') {
            $('.input__bet').val(1);
        } else if (type === 'max') {
            const balance = parseFloat($('#balance').text()) || 0;
            $('.input__bet').val(balance);
        }
    };
    
    // Bet selection
    window.selectBet = function(bet) {
        if (isPlaying) return;
        
        selectedBet = bet;
        
        // Update UI
        $('.baccarat__bet_btn').removeClass('selected');
        $(`#bet-${bet}`).addClass('selected');
        
        // Enable play button
        $('#play-btn').prop('disabled', false);
        
        updateStatusText(`Выбрано: ${getBetDisplayName(bet)}. Нажмите "Играть"`);
    };
    
    // Play button click
    $('#play-btn').click(function() {
        if (!selectedBet || isPlaying) return;
        
        const bet = parseFloat($('.input__bet').val());
        
        if (!bet || bet < 1) {
            showNotification('Введите корректную ставку', 'error');
            return;
        }
        
        playBaccarat(bet, selectedBet);
    });
    
    function playBaccarat(bet, choice) {
        isPlaying = true;
        $('#play-btn').prop('disabled', true);
        $('.baccarat__bet_btn').prop('disabled', true);
        
        updateStatusText('Раздача карт...');
        clearCards();
        
        // Make API call
        $.post('/baccarat/play', {
            _token: $('meta[name="csrf-token"]').attr('content'),
            bet: bet,
            choice: choice
        }).then(response => {
            if (response.type === 'success') {
                dealCards(response);
            } else {
                showNotification(response.msg, 'error');
                resetGame();
            }
        }).catch(error => {
            showNotification('Ошибка при игре', 'error');
            resetGame();
        });
    }
    
    function dealCards(response) {
        // Deal initial cards with animation
        setTimeout(() => {
            displayCard(response.player_cards[0], 'player');
        }, 500);
        
        setTimeout(() => {
            displayCard(response.banker_cards[0], 'banker');
        }, 1000);
        
        setTimeout(() => {
            displayCard(response.player_cards[1], 'player');
        }, 1500);
        
        setTimeout(() => {
            displayCard(response.banker_cards[1], 'banker');
        }, 2000);
        
        // Update totals after initial cards
        setTimeout(() => {
            updateTotals(response.player_cards.slice(0, 2), response.banker_cards.slice(0, 2));
        }, 2500);
        
        // Deal third cards if any
        let delay = 3000;
        if (response.player_third_card) {
            setTimeout(() => {
                displayCard(response.player_third_card, 'player');
                updateStatusText('Игрок берет третью карту');
            }, delay);
            delay += 1000;
        }
        
        if (response.banker_third_card) {
            setTimeout(() => {
                displayCard(response.banker_third_card, 'banker');
                updateStatusText('Банкир берет третью карту');
            }, delay);
            delay += 1000;
        }
        
        // Show final result
        setTimeout(() => {
            showResult(response);
        }, delay + 500);
    }
    
    function displayCard(card, section) {
        const isRed = card.suit === 'hearts' || card.suit === 'diamonds';
        const suitSymbol = getSuitSymbol(card.suit);
        
        const cardElement = `
            <div class="baccarat__card ${isRed ? 'red' : 'black'}">
                <div class="baccarat__card_rank">${card.rank}</div>
                <div class="baccarat__card_suit">${suitSymbol}</div>
                <div class="baccarat__card_rank" style="transform: rotate(180deg);">${card.rank}</div>
            </div>
        `;
        
        $(`#${section}-cards`).append(cardElement);
    }
    
    function updateTotals(playerCards, bankerCards) {
        const playerTotal = calculateBaccaratTotal(playerCards);
        const bankerTotal = calculateBaccaratTotal(bankerCards);
        
        $('#player-total').text(playerTotal);
        $('#banker-total').text(bankerTotal);
    }
    
    function calculateBaccaratTotal(cards) {
        let total = 0;
        cards.forEach(card => {
            let value = 0;
            if (['J', 'Q', 'K'].includes(card.rank)) {
                value = 0;
            } else if (card.rank === 'A') {
                value = 1;
            } else {
                value = parseInt(card.rank);
            }
            total += value;
        });
        return total % 10;
    }
    
    function showResult(response) {
        // Update final totals
        $('#player-total').text(response.player_total);
        $('#banker-total').text(response.banker_total);
        
        // Show result message
        let resultText = `Результат: ${response.winner_display} (${response.player_total} - ${response.banker_total})`;
        if (response.is_win) {
            resultText += ` - Вы выиграли ${response.win_amount} ₽!`;
            showNotification(`Поздравляем! Выигрыш: ${response.win_amount} ₽`, 'win');
        } else {
            resultText += ` - Вы проиграли ${response.game.bet} ₽`;
            showNotification(`Не повезло. Попробуйте еще раз!`, 'lose');
        }
        
        updateStatusText(resultText);
        updateBalance(response.balance);
        
        // Update stats and history
        updateStats(response.winner);
        addToHistory(response.game);
        addToRecentResults(response.winner);
        
        // Reset game after delay
        setTimeout(() => {
            resetGame();
        }, 4000);
    }
    
    function clearCards() {
        $('#player-cards').empty();
        $('#banker-cards').empty();
        $('#player-total').text('0');
        $('#banker-total').text('0');
    }
    
    function resetGame() {
        isPlaying = false;
        selectedBet = null;
        
        $('.baccarat__bet_btn').removeClass('selected').prop('disabled', false);
        $('#play-btn').prop('disabled', true);
        
        updateStatusText('Выберите ставку и нажмите "Играть"');
    }
    
    function updateStatusText(text) {
        $('#game-status .baccarat__status_text').text(text);
    }
    
    function updateStats(winner) {
        stats[winner]++;
        stats.total++;
        
        $('#player-wins').text(stats.player);
        $('#banker-wins').text(stats.banker);
        $('#tie-wins').text(stats.tie);
        $('#total-games').text(stats.total);
    }
    
    function loadStats() {
        $.get('/baccarat/recent-results').then(response => {
            if (response.type === 'success') {
                stats = { player: 0, banker: 0, tie: 0, total: 0 };
                
                response.results.forEach(result => {
                    stats[result.winner]++;
                    stats.total++;
                });
                
                $('#player-wins').text(stats.player);
                $('#banker-wins').text(stats.banker);
                $('#tie-wins').text(stats.tie);
                $('#total-games').text(stats.total);
            }
        });
    }
    
    function loadGameHistory() {
        $.get('/baccarat/history').then(response => {
            if (response.type === 'success') {
                displayGameHistory(response.history);
            }
        });
    }
    
    function loadRecentResults() {
        $.get('/baccarat/recent-results').then(response => {
            if (response.type === 'success') {
                displayRecentResults(response.results.slice(0, 20));
            }
        });
    }
    
    function displayGameHistory(history) {
        const container = $('#game-history');
        container.empty();
        
        history.forEach(game => {
            const historyItem = `
                <div class="baccarat__history_item ${game.status}">
                    <div class="baccarat__history_details">
                        <div class="baccarat__history_choice">
                            Ставка: ${game.choice_display}
                        </div>
                        <div class="baccarat__history_result">
                            Результат: ${game.winner_display} (${game.player_total}-${game.banker_total})
                        </div>
                    </div>
                    <div class="baccarat__history_amount">
                        ${game.status === 'win' ? '+' : '-'}${game.status === 'win' ? game.win : game.bet} ₽
                    </div>
                </div>
            `;
            container.append(historyItem);
        });
    }
    
    function displayRecentResults(results) {
        const container = $('#recent-results');
        container.empty();
        
        results.forEach(result => {
            const resultItem = `
                <div class="baccarat__recent_item ${result.winner}">
                    ${getWinnerSymbol(result.winner)}
                </div>
            `;
            container.append(resultItem);
        });
    }
    
    function addToHistory(game) {
        const historyItem = `
            <div class="baccarat__history_item ${game.status}">
                <div class="baccarat__history_details">
                    <div class="baccarat__history_choice">
                        Ставка: ${getBetDisplayName(game.choice)}
                    </div>
                    <div class="baccarat__history_result">
                        Результат: ${getBetDisplayName(game.winner)} (${game.player_total}-${game.banker_total})
                    </div>
                </div>
                <div class="baccarat__history_amount">
                    ${game.status === 'win' ? '+' : '-'}${game.status === 'win' ? game.win : game.bet} ₽
                </div>
            </div>
        `;
        
        $('#game-history').prepend(historyItem);
        
        // Keep only last 10 items
        $('#game-history .baccarat__history_item').slice(10).remove();
    }
    
    function addToRecentResults(winner) {
        const resultItem = `
            <div class="baccarat__recent_item ${winner}">
                ${getWinnerSymbol(winner)}
            </div>
        `;
        
        $('#recent-results').prepend(resultItem);
        
        // Keep only last 20 items
        $('#recent-results .baccarat__recent_item').slice(20).remove();
    }
    
    function getBetDisplayName(bet) {
        const names = {
            'player': 'Игрок',
            'banker': 'Банкир',
            'tie': 'Ничья'
        };
        return names[bet] || bet;
    }
    
    function getWinnerSymbol(winner) {
        const symbols = {
            'player': 'И',
            'banker': 'Б',
            'tie': 'Н'
        };
        return symbols[winner] || winner;
    }
    
    function getSuitSymbol(suit) {
        const symbols = {
            'hearts': '♥',
            'diamonds': '♦',
            'clubs': '♣',
            'spades': '♠'
        };
        return symbols[suit] || suit;
    }
    
    function showNotification(message, type) {
        const notification = $(`
            <div class="baccarat__notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 3000);
    }
    
    function updateBalance(newBalance) {
        $('#balance').text(newBalance.toFixed(2));
    }
});

// Additional CSS for notifications
$('<style>').text(`
    .baccarat__notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }
    
    .baccarat__notification.error { background: #dc3545; }
    .baccarat__notification.win { background: #28a745; }
    .baccarat__notification.lose { background: #6c757d; }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`).appendTo('head');
