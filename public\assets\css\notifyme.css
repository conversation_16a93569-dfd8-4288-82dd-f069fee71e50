@import url('https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

.notifyme-notification,.notifyme-notification *,.notifyme-notification *:after,.notifyme-notification *:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.notifyme-notification.notifyme-hide {
    -webkit-animation-name: animFade;
    animation-name: animFade;
    -webkit-animation-duration: .25s;
    animation-duration: .25s;
    -webkit-animation-direction: reverse;
    animation-direction: reverse;
}

.notifyme-notification.notifyme-show {
    -webkit-animation-name: animJelly;
    animation-name: animJelly;
    -webkit-animation-duration: .1s;
    animation-duration: .1s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
}

.notifyme-notification.notifyme-show {
    pointer-events: auto;
}

.notifyme-notification {
    border-radius: 10px;
    position: fixed;
    background: rgba(42,45,50,0.85);
    padding: 22px;
    line-height: 1.4;
    z-index: 1000;
    pointer-events: none;
    color: rgba(250,251,255,0.95);
    font-size: 100%;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    max-width: 370px;
    z-index: 9999999999999;
}

.notifyme-notification p {
    margin: 0;
    line-height: 1.3;
    font-size: 14px;
}

.notifyme-notification a {
    opacity: .7;
    font-weight: 700;
    text-decoration: none;
}

.notifyme-notification a:hover,.notifyme-notification a:focus {
    color: #fff!important;
    opacity: 1;
}

.notifyme-close {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 4px;
    top: 4px;
    overflow: hidden;
    text-indent: 100%;
    cursor: pointer;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.notifyme-close::before,.notifyme-close::after {
    content: '';
    position: absolute;
    width: 3px;
    height: 60%;
    top: 50%;
    left: 50%;
    background: #6e6e6e;
}

.notifyme-close::after {
    -webkit-transform: translate(-50%,-50%) rotate(-45deg);
    transform: translate(-50%,-50%) rotate(-45deg);
}

.notifyme-close::before {
    -webkit-transform: translate(-50%,-50%) rotate(45deg);
    transform: translate(-50%,-50%) rotate(45deg);
}

.notifyme-close:hover,.notifyme-close:focus {
    outline: 0;
}

.notifyme-close:hover::before,.notifyme-close:hover::after {
    background: #fff!important;
}

@-webkit-keyframes animFade {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes animFade {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@-webkit-keyframes animJelly {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes animJelly {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.notifyme-notification .notifyme-wrapper i.fa.notifyme-icon {
    position: absolute;
    left: 20px;
    top: 53%;
    font-size: 22px;
    margin-top: -11px;
}

.notifyme-wrapper {
    padding-left: 15px;
    padding-right: 5px;
}

.notifyme-notification.notifyme-success {
    background: rgba(22, 145, 81, .7);
}

.notifyme-notification.notifyme-success .notifyme-close::before,.notifyme-notification.notifyme-success .notifyme-close::after {
    background: #FFFFFF;
}

.notifyme-notification.notifyme-success a {
    color: #007330;
}

.notifyme-notification.notifyme-error {
    background: rgba(216,51,51,.7);
}

.notifyme-notification.notifyme-error .notifyme-close::before,.notifyme-notification.notifyme-error .notifyme-close::after {
    background: #FFFFFF;
}

.notifyme-notification.notifyme-error a {
    color: #7c1313;
}

.notifyme-notification.notifyme-warning {
    background: #ffe008;
    color: #c8922f;
}

.notifyme-notification.notifyme-warning .notifyme-close::before,.notifyme-notification.notifyme-warning .notifyme-close::after {
    background: #c8922f;
}

.notifyme-notification.notifyme-warning a {
    color: #a97515;
}

.notifyme-notification.notifyme-top-left {
    top: 20px;
    left: 20px;
}

.notifyme-notification.notifyme-top-right {
    top: 20px;
    right: 20px;
}

.notifyme-notification.notifyme-bottom-left {
    bottom: 20px;
    left: 20px;
}

.notifyme-notification.notifyme-bottom-right {
    bottom: 20px;
    right: 20px;
}