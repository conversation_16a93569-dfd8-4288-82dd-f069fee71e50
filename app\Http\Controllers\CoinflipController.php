<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Coinflip;
use App\Models\User;
use App\Models\Settings;
use App\Models\Profit;

class CoinflipController extends Controller
{
    /**
     * Flip the coin
     */
    public function flip(Request $request)
    {
        $settings = Settings::where('id', 1)->first();
        // Guest mode - create virtual user if not authenticated
        if (!Auth::check()) {
            $guestUser = new \App\Models\User();
            $guestUser->id = 999999;
            $guestUser->username = 'Guest_' . uniqid();
            $guestUser->balance = 10000.00;
            $guestUser->is_guest = true;
            Auth::setUser($guestUser);
        }
        
        if ($settings && isset($settings->coinflip_enabled) && $settings->coinflip_enabled == 1) {
            return response()->json(['type' => 'error', 'msg' => 'В данный момент режим недоступен!']);
        }

        $bet = $request->bet;
        $choice = $request->choice; // 'heads' or 'tails'
        $user = Auth::user();

        // Validation
        if ($bet < 1 || !is_numeric($bet)) {
            return response()->json(['type' => 'error', 'msg' => 'Минимальная сумма ставки - 1 рубль!']);
        }
        
        if ($bet > $user->balance) {
            return response()->json(['type' => 'error', 'msg' => 'Недостаточно средств']);
        }

        if (!in_array($choice, ['heads', 'tails'])) {
            return response()->json(['type' => 'error', 'msg' => 'Неверный выбор стороны монеты']);
        }

        DB::beginTransaction();
        try {
            // Deduct bet from user balance
            $user->balance -= $bet;
            $user->save();

            // Flip the coin
            $result = Coinflip::flipCoin();
            $isWin = ($choice === $result);
            $winAmount = 0;

            if ($isWin) {
                $winAmount = $bet * 2; // 1:1 payout + original bet
                $user->balance += $winAmount;
                $user->save();
                $this->recordProfit(-$bet); // House loses
            } else {
                $this->recordProfit($bet); // House wins
            }

            // Create game record
            $game = Coinflip::create([
                'user_id' => $user->id,
                'bet' => $bet,
                'choice' => $choice,
                'result' => $result,
                'status' => $isWin ? 'win' : 'lose',
                'win' => $winAmount
            ]);

            DB::commit();

            return response()->json([
                'type' => 'success',
                'game' => $game,
                'result' => $result,
                'choice' => $choice,
                'is_win' => $isWin,
                'win_amount' => $winAmount,
                'balance' => $user->balance,
                'result_display' => Coinflip::getDisplayName($result),
                'choice_display' => Coinflip::getDisplayName($choice)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['type' => 'error', 'msg' => 'Ошибка при игре']);
        }
    }

    /**
     * Get game history
     */
    public function history(Request $request)
    {
        // Guest mode - create virtual user if not authenticated
        if (!Auth::check()) {
            $guestUser = new \App\Models\User();
            $guestUser->id = 999999;
            $guestUser->username = 'Guest_' . uniqid();
            $guestUser->balance = 10000.00;
            $guestUser->is_guest = true;
            Auth::setUser($guestUser);
        }

        $history = Coinflip::where('user_id', Auth::user()->id)
                          ->orderBy('id', 'desc')
                          ->limit(20)
                          ->get();

        $historyWithDisplay = $history->map(function ($game) {
            return [
                'id' => $game->id,
                'bet' => $game->bet,
                'choice' => $game->choice,
                'result' => $game->result,
                'status' => $game->status,
                'win' => $game->win,
                'choice_display' => Coinflip::getDisplayName($game->choice),
                'result_display' => Coinflip::getDisplayName($game->result),
                'created_at' => $game->created_at
            ];
        });

        return response()->json([
            'type' => 'success',
            'history' => $historyWithDisplay
        ]);
    }

    /**
     * Get recent global results
     */
    public function recentResults(Request $request)
    {
        $results = Coinflip::orderBy('id', 'desc')
                          ->limit(50)
                          ->get(['result', 'created_at']);

        return response()->json([
            'type' => 'success',
            'results' => $results
        ]);
    }

    /**
     * Record profit for the house
     */
    private function recordProfit($amount)
    {
        Profit::create([
            'game' => 'coinflip',
            'sum' => $amount
        ]);
    }
}
