{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"axios": "^1.1.2", "laravel-vite-plugin": "^0.7.2", "lodash": "^4.17.19", "postcss": "^8.1.14", "vite": "^4.0.0", "webpack": "^5.79.0", "webpack-cli": "^5.0.1"}, "dependencies": {"clientjs": "^0.2.1", "express": "^4.18.2", "laravel-mix": "^6.0.49", "mysql": "^2.18.1", "node-telegram-bot-api": "^0.61.0", "notifyme-js": "^2.0.0", "pm2": "^5.2.2", "redis": "^3.0.2", "requestify": "^0.2.5", "socket.io": "^4.6.1"}, "name": "zubrix", "description": "", "version": "1.0.0", "main": "webpack.config.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC"}